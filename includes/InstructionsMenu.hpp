#ifndef INSTRUCTIONSMENU_HPP
#define INSTRUCTIONSMENU_HPP

// Use CStyle includes for all dependencies
#include "CStyle/humangl.hpp"

class InstructionsMenu : public BaseMenu {
private:
    InstructionsMenuRenderer& renderer;

public:
    InstructionsMenu(InstructionsMenuRenderer& renderer, MouseHandler& mouseHandler, MenuInput& menuInput, int winWidth, int winHeight);
    virtual ~InstructionsMenu() = default;

    // Implement pure virtual methods from BaseMenu
    void initializeButtons() override;
    void render() override;
    MenuAction handleButtonClick(int buttonIndex) override;
};

#endif // INSTRUCTIONSMENU_HPP
