#ifndef MENU_SYSTEM_HPP
#define MENU_SYSTEM_HPP

// Use CStyle includes for all dependencies
#include "CStyle/humangl.hpp"

class MenuSystem {
private:
    // Static instances of utility classes
    Text<PERSON>enderer textRenderer;
    MainMenuRenderer mainMenuRenderer;
    SettingsMenuRenderer settingsMenuRenderer;
    CreditsMenuRenderer creditsMenuRenderer;
    InstructionsMenuRenderer instructionsMenuRenderer;
    MouseHandler mouseHandler;
    MenuInput menuInput;

    // Menu instances
    MainMenu mainMenu;
    SettingsMenu settingsMenu;
    CreditsMenu creditsMenu;
    InstructionsMenu instructionsMenu;
    
    // Current state
    AppState currentState;
    int windowWidth;
    int windowHeight;

public:
    MenuSystem();
    ~MenuSystem();
    
    // Initialize the menu system
    bool initialize(int winWidth, int winHeight);
    
    // Clean up resources
    void cleanup();
    
    // Update window dimensions
    void updateWindowSize(int width, int height);
    
    // Set current menu state
    void setState(AppState state);
    AppState getState() const;
    
    // Handle SDL events
    MenuAction handleEvent(const SDL_Event& event);
    
    // Update menu system (call each frame)
    MenuAction update();
    
    // Render current menu
    void render();
};

#endif // MENU_SYSTEM_HPP
