#pragma once

// Use CStyle includes for all basic dependencies
#include "CStyle/humangl.hpp"

// Include specific headers needed
#include "TextRenderer.hpp"
#include "MenuRenderer.hpp"
#include "MainMenuRenderer.hpp"
#include "SettingsMenuRenderer.hpp"
#include "CreditsMenuRenderer.hpp"
#include "InstructionsMenuRenderer.hpp"
#include "MouseHandler.hpp"
#include "MenuInput.hpp"
#include "BaseMenu.hpp"
#include "MainMenu.hpp"
#include "SettingsMenu.hpp"
#include "CreditsMenu.hpp"
#include "InstructionsMenu.hpp"

class MenuSystem {
private:
    // Static instances of utility classes
    TextRenderer textRenderer;
    MainMenuRenderer mainMenuRenderer;
    SettingsMenuRenderer settingsMenuRenderer;
    CreditsMenuRenderer creditsMenuRenderer;
    InstructionsMenuRenderer instructionsMenuRenderer;
    MouseHandler mouseHandler;
    MenuInput menuInput;

    // Menu instances
    MainMenu mainMenu;
    SettingsMenu settingsMenu;
    CreditsMenu creditsMenu;
    InstructionsMenu instructionsMenu;
    
    // Current state
    AppState currentState;
    int windowWidth;
    int windowHeight;

public:
    MenuSystem();
    ~MenuSystem();
    
    // Initialize the menu system
    bool initialize(int winWidth, int winHeight);
    
    // Clean up resources
    void cleanup();
    
    // Update window dimensions
    void updateWindowSize(int width, int height);
    
    // Set current menu state
    void setState(AppState state);
    AppState getState() const;
    
    // Handle SDL events
    MenuAction handleEvent(const SDL_Event& event);
    
    // Update menu system (call each frame)
    MenuAction update();
    
    // Render current menu
    void render();
};


