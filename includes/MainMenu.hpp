#pragma once

// Use CStyle includes for all basic dependencies
#include "CStyle/humangl.hpp"

// Include specific dependency
#include "BaseMenu.hpp"

// Forward declarations
class MainMenuRenderer;
class MouseHandler;
class MenuInput;

class MainMenu : public BaseMenu {
private:
    MainMenuRenderer& renderer;

public:
    MainMenu(MainMenuRenderer& renderer, MouseHandler& mouseHandler, MenuInput& menuInput, int winWidth, int winHeight);
    virtual ~MainMenu() = default;

    // Implement pure virtual methods from BaseMenu
    void initializeButtons() override;
    void render() override;
    MenuAction handleButtonClick(int buttonIndex) override;
};


