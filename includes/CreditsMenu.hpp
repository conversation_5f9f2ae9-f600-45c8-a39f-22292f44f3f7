#ifndef CREDITSMENU_HPP
#define CREDITSMENU_HPP

// Use CStyle includes for all dependencies
#include "CStyle/humangl.hpp"

class CreditsMenu : public BaseMenu {
private:
    CreditsMenuRenderer& renderer;

public:
    CreditsMenu(CreditsMenuRenderer& renderer, MouseHandler& mouseHandler, MenuInput& menuInput, int winWidth, int winHeight);
    virtual ~CreditsMenu() = default;

    // Implement pure virtual methods from BaseMenu
    void initializeButtons() override;
    void render() override;
    MenuAction handleButtonClick(int buttonIndex) override;
};

#endif // CREDITSMENU_HPP
