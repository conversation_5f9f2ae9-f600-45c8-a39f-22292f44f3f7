#ifndef BASEMENU_HPP
#define BASEMENU_HPP

#include "MenuTypes.hpp"
#include <SDL2/SDL.h>
#include <vector>

// Forward declarations
class MouseHandler;
class MenuInput;

class BaseMenu {
protected:
    std::vector<MenuButton> buttons;
    Mouse<PERSON><PERSON>ler& mouseHandler;
    MenuInput& menuInput;
    int windowWidth;
    int windowHeight;

public:
    BaseMenu(MouseHandler& mouseHandler, MenuInput& menuInput, int winWidth, int winHeight);
    virtual ~BaseMenu() = default;

    // Pure virtual methods that must be implemented by derived classes
    virtual void initializeButtons() = 0;
    virtual void render() = 0;
    virtual MenuAction handleButtonClick(int buttonIndex) = 0;

    // Common functionality
    void updateWindowSize(int width, int height);
    const std::vector<MenuButton>& getButtons() const;
    MenuAction handleEvent(const SDL_Event& event);
    MenuAction update();

protected:
    // Helper methods for derived classes
    void updateButtonHover();
    int checkButtonClick();
};

#endif // BASEMENU_HPP
