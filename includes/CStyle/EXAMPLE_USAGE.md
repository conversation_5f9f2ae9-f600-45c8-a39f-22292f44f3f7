# CStyle Header System - Usage Examples

## Complete Working Example

Here's how the new CStyle header system works in practice:

### 1. Main Application (src/main.cpp)
```cpp
// HumanGL - Skeletal Animation System
// Single include file for all project dependencies
#include "../includes/CStyle/humangl.hpp"

class Application {
    MenuSystem menuSystem;  // Available from humangl.hpp
    AnimationMode currentMode = WALKING;  // Enum from CStyle/enums.hpp
    
public:
    bool initialize() {
        // Use constants from CStyle/defines.hpp
        int width = HUMANGL_DEFAULT_WINDOW_WIDTH;
        int height = HUMANGL_DEFAULT_WINDOW_HEIGHT;
        
        // Use utility functions from humangl.hpp
        Color bgColor = Color::SkinTone();
        setGLClearColor(bgColor);
        
        return menuSystem.initialize(width, height);
    }
};
```

### 2. Header Files (includes/*.hpp)
```cpp
// includes/MenuRenderer.hpp
#ifndef MENU_RENDERER_HPP
#define MENU_RENDERER_HPP

// Use CStyle includes for basic dependencies
#include "CStyle/libs.hpp"
#include "CStyle/structs.hpp"

// Forward declaration
class TextRenderer;

class MenuRenderer {
    TextRenderer& textRenderer;
public:
    MenuRenderer(TextRenderer& textRend);
    virtual void render(const std::vector<MenuButton>& buttons) = 0;
};

#endif
```

### 3. Source Files (sources/*.cpp)
```cpp
// sources/MenuRenderer.cpp
// Use the corresponding header file which includes all necessary CStyle dependencies
#include "../includes/MenuRenderer.hpp"

// Include TextRenderer for the implementation
#include "../includes/TextRenderer.hpp"

MenuRenderer::MenuRenderer(TextRenderer& textRend) : textRenderer(textRend) {
    // Implementation uses MenuButton from CStyle/structs.hpp
    // Uses OpenGL functions from CStyle/libs.hpp
}
```

## Key Benefits Demonstrated

### ✅ Simplified Includes
- **Before**: 10+ individual includes per file
- **After**: 1 include for main app, corresponding .hpp for source files

### ✅ Consistent Dependencies
- All files use the same library versions
- No missing includes or version conflicts
- Platform-specific code handled automatically

### ✅ Better Organization
- **defines.hpp**: All constants and macros
- **libs.hpp**: All library includes
- **enums.hpp**: All enumerations
- **structs.hpp**: All data structures
- **humangl.hpp**: Master include with utilities

### ✅ No Circular Dependencies
- Forward declarations in headers
- Full definitions in source files
- Clean dependency hierarchy

## Enum Consolidation Example

All enums are now centralized in `CStyle/enums.hpp`:

```cpp
// From MenuTypes.hpp (now consolidated)
enum AppState { MAIN_MENU, SIMULATION, SETTINGS, CREDITS, INSTRUCTIONS };
enum MenuAction { MENU_ACTION_NONE, MENU_ACTION_START_SIMULATION, /* ... */ };

// From main.cpp Application class (now consolidated)
enum AnimationMode { NONE, WALKING, JUMPING, DANCING, KUNGFU };

// New enums for future expansion
enum BodyPart { BODY_PART_HEAD, BODY_PART_TORSO, /* ... */ };
enum InputKey { INPUT_KEY_W, INPUT_KEY_A, INPUT_KEY_S, INPUT_KEY_D, /* ... */ };
```

## Struct Consolidation Example

All structs are now centralized in `CStyle/structs.hpp`:

```cpp
// From MenuTypes.hpp (now consolidated)
struct MenuButton {
    float x, y, width, height;
    std::string text;
    bool hovered;
    // ... methods
};

// New utility structures
struct Color {
    float r, g, b, a;
    static Color SkinTone() { return Color(0.9f, 0.7f, 0.6f, 1.0f); }
    // ... methods
};

struct Vector3 {
    float x, y, z;
    Vector3 operator+(const Vector3& other) const;
    // ... methods
};
```

## Migration Pattern

### Step 1: Update Header Files
```cpp
// OLD
#include <SDL2/SDL.h>
#include <vector>
#include "MenuTypes.hpp"

// NEW
#include "CStyle/libs.hpp"
#include "CStyle/structs.hpp"
// Forward declarations as needed
```

### Step 2: Update Source Files
```cpp
// OLD
#include "../includes/MenuRenderer.hpp"
#include <SDL2/SDL.h>
#include <vector>
#include <string>

// NEW
#include "../includes/MenuRenderer.hpp"
#include "../includes/TextRenderer.hpp"  // Only if needed for implementation
```

### Step 3: Use Utility Functions
```cpp
// Instead of manual OpenGL calls
glColor4f(0.9f, 0.7f, 0.6f, 1.0f);

// Use utility functions
setGLColor(Color::SkinTone());
```

## Compilation Results

✅ **Project compiles successfully**  
✅ **All 17 source files compile**  
✅ **Executable created: humangl (192KB)**  
✅ **Only minor warnings (floating-point precision, deprecated OpenGL)**  
✅ **No errors or missing dependencies**  

## File Structure Summary

```
includes/CStyle/
├── defines.hpp     ✅ All constants and macros
├── libs.hpp        ✅ All library includes  
├── enums.hpp       ✅ All project enumerations
├── structs.hpp     ✅ All data structures
├── humangl.hpp     ✅ Master include + utilities
├── README.md       ✅ Complete documentation
└── EXAMPLE_USAGE.md ✅ This file

includes/
├── *.hpp           ✅ Updated to use CStyle includes
└── MenuTypes.hpp   ✅ Removed (consolidated into CStyle)

sources/
└── *.cpp           ✅ Updated to include only corresponding .hpp
```

This system provides a clean, maintainable, and scalable approach to header management for your HumanGL skeletal animation project!
