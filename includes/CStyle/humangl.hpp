#ifndef HUMANGL_HPP
#define HUMANGL_HPP

/*
 * HumanGL - Skeletal Animation System
 * 
 * Master include file for the HumanGL project.
 * This file includes all necessary headers in the correct order.
 * 
 * Usage:
 *   #include "CStyle/humangl.hpp"
 * 
 * This replaces the need for multiple individual includes in each source file.
 * 
 * Author: HumanGL Development Team
 * Version: 1.0
 * Date: 2025
 */

// ============================================================================
// CORE SYSTEM HEADERS (Order matters!)
// ============================================================================

// 1. Defines and constants (must come first)
#include "defines.hpp"

// 2. Library includes (SDL2, OpenGL, STL)
#include "libs.hpp"

// 3. Enumerations (depend on basic types)
#include "enums.hpp"

// 4. Structures (depend on enums and libs)
#include "structs.hpp"

// ============================================================================
// PROJECT-SPECIFIC HEADERS
// ============================================================================

// Math and utility classes
#include "../Matrix4.hpp"
#include "../MatrixStack.hpp"

// Menu system headers (MenuTypes.hpp content now in CStyle/enums.hpp)
// Include in dependency order: base utilities first, then classes that depend on them
#include "../TextRenderer.hpp"
#include "../MouseHandler.hpp"
#include "../MenuInput.hpp"
#include "../MenuRenderer.hpp"
#include "../MainMenuRenderer.hpp"
#include "../SettingsMenuRenderer.hpp"
#include "../CreditsMenuRenderer.hpp"
#include "../InstructionsMenuRenderer.hpp"
#include "../BaseMenu.hpp"
#include "../MainMenu.hpp"
#include "../SettingsMenu.hpp"
#include "../CreditsMenu.hpp"
#include "../InstructionsMenu.hpp"
#include "../MenuSystem.hpp"

// ============================================================================
// CONVENIENCE MACROS AND INLINE FUNCTIONS
// ============================================================================

// Convenience macros for common operations
#define HUMANGL_UNUSED(x) ((void)(x))

// Inline utility functions
inline float clamp(float value, float min, float max) {
    return (value < min) ? min : (value > max) ? max : value;
}

inline float lerp(float a, float b, float t) {
    return a + t * (b - a);
}

inline float smoothstep(float edge0, float edge1, float x) {
    float t = clamp((x - edge0) / (edge1 - edge0), 0.0f, 1.0f);
    return t * t * (3.0f - 2.0f * t);
}

// Color conversion utilities
inline void setGLColor(const Color& color) {
    glColor4f(color.r, color.g, color.b, color.a);
}

inline void setGLClearColor(const Color& color) {
    glClearColor(color.r, color.g, color.b, color.a);
}

// Vector utilities
inline void glTranslatev(const Vector3& v) {
    glTranslatef(v.x, v.y, v.z);
}

inline void glRotatev(float angle, const Vector3& axis) {
    glRotatef(angle, axis.x, axis.y, axis.z);
}

inline void glScalev(const Vector3& v) {
    glScalef(v.x, v.y, v.z);
}

// ============================================================================
// VERSION AND BUILD INFORMATION
// ============================================================================

#define HUMANGL_VERSION_MAJOR 1
#define HUMANGL_VERSION_MINOR 0
#define HUMANGL_VERSION_PATCH 0
#define HUMANGL_VERSION_STRING "1.0.0"

// Build configuration
#ifdef DEBUG
    #define HUMANGL_BUILD_TYPE "Debug"
#else
    #define HUMANGL_BUILD_TYPE "Release"
#endif

// Platform detection
#ifdef __APPLE__
    #define HUMANGL_PLATFORM "macOS"
#elif defined(_WIN32)
    #define HUMANGL_PLATFORM "Windows"
#elif defined(__linux__)
    #define HUMANGL_PLATFORM "Linux"
#else
    #define HUMANGL_PLATFORM "Unknown"
#endif

// ============================================================================
// NAMESPACE (Optional - for future C++ style organization)
// ============================================================================

namespace HumanGL {
    // Future C++ style classes and functions can go here
    // For now, we maintain C-style for compatibility
}

#endif // HUMANGL_HPP
