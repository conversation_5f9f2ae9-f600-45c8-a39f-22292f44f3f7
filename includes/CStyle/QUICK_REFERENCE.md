# CStyle Header System - Quick Reference

## 🚀 **Quick Start Guide**

### **For New .hpp Files:**
```cpp
#pragma once
#include "CStyle/humangl.hpp"
// Forward declarations as needed
// Your class definition
```

### **For New .cpp Files:**
```cpp
#include "../includes/YourClass.hpp"
// Additional includes only if needed for implementation
```

### **For Main Application Files:**
```cpp
#include "../includes/CStyle/humangl.hpp"
#include "../includes/SpecificClass.hpp"  // Only what you need
```

---

## 📋 **What's Available in `humangl.hpp`**

### **From `defines.hpp`:**
- `HUMANGL_DEFAULT_WINDOW_WIDTH` (800)
- `HUMANGL_DEFAULT_WINDOW_HEIGHT` (600)
- `HUMANGL_DEFAULT_FOV` (45.0f)
- `HUMANGL_NEAR_PLANE` (0.1f)
- `HUMANGL_FAR_PLANE` (100.0f)
- `M_PI` (3.14159265358979323846) - Linux only

### **From `libs.hpp`:**
- SDL2 (SDL.h, SDL_opengl.h)
- OpenGL (gl.h, glu.h)
- STL (iostream, vector, string, cmath, etc.)

### **From `enums.hpp`:**
- `AppState` (MAIN_MENU, SIMULATION, SETTINGS, etc.)
- `MenuAction` (MENU_ACTION_NONE, MENU_ACTION_START_SIMULATION, etc.)
- `AnimationMode` (NONE, WALKING, JUMPING, DANCING, KUNGFU)
- `SettingsPage` (SETTINGS_MAIN, SETTINGS_GRAPHICS, etc.)
- `BodyPart`, `InputKey`, `RenderMode`, `QualityLevel`

### **From `structs.hpp`:**
- `MenuButton` (x, y, width, height, text, hovered)
- `Color` (r, g, b, a + static methods like SkinTone())
- `Vector3` (x, y, z + operators)
- `BodyPartSettings`, `AnimationKeyframe`, `WindowSettings`

---

## 🛠️ **Common Tasks**

### **Add New Constant:**
Edit `includes/CStyle/defines.hpp`:
```cpp
#define YOUR_NEW_CONSTANT 42
```

### **Add New Enum:**
Edit `includes/CStyle/enums.hpp`:
```cpp
enum YourNewEnum {
    YOUR_OPTION_1,
    YOUR_OPTION_2
};
```

### **Add New Struct:**
Edit `includes/CStyle/structs.hpp`:
```cpp
struct YourNewStruct {
    int value;
    std::string name;
    
    YourNewStruct(int v, const std::string& n) : value(v), name(n) {}
};
```

### **Add New Library:**
Edit `includes/CStyle/libs.hpp`:
```cpp
#include <your_new_library.h>
```

---

## 🔍 **Troubleshooting**

### **Compilation Error: "undefined reference"**
- Make sure your .cpp file includes its corresponding .hpp file
- Check if you need additional implementation includes

### **Compilation Error: "incomplete type"**
- Add forward declaration in .hpp file
- Include full definition in .cpp file

### **Compilation Error: "redefinition"**
- Check for duplicate definitions
- Ensure `#pragma once` is at the top of all headers

### **Want to Add New Project Header:**
1. Create `includes/YourClass.hpp` with `#pragma once` and `#include "CStyle/humangl.hpp"`
2. Create `sources/YourClass.cpp` with `#include "../includes/YourClass.hpp"`
3. Add to Makefile if needed

---

## 📁 **File Organization Rules**

### **CStyle Files (Core System):**
- `defines.hpp` - Constants, macros, #defines
- `libs.hpp` - Library includes only
- `enums.hpp` - All enumerations
- `structs.hpp` - All data structures
- `humangl.hpp` - Includes the 4 above only

### **Project Headers:**
- Include `CStyle/humangl.hpp` for basics
- Add specific includes as needed
- Use forward declarations when possible

### **Source Files:**
- Include corresponding .hpp file first
- Add implementation-specific includes as needed
- Never include `CStyle/humangl.hpp` directly

---

## ⚡ **Performance Tips**

1. **Use Forward Declarations** in .hpp files when possible
2. **Include Implementation Dependencies** only in .cpp files
3. **Keep CStyle Files Clean** - don't add project-specific code
4. **Use `#pragma once`** for all new headers

---

## 🎯 **Best Practices**

✅ **DO:**
- Use `#pragma once` in all headers
- Include `CStyle/humangl.hpp` in project headers
- Keep .cpp includes minimal
- Add new enums/structs to CStyle files

❌ **DON'T:**
- Include `CStyle/humangl.hpp` directly in .cpp files
- Add project-specific code to CStyle files
- Use traditional header guards
- Include unnecessary headers

---

## 🏆 **Current Status**

✅ **17 source files** compile successfully  
✅ **All headers** use `#pragma once`  
✅ **Clean dependency hierarchy**  
✅ **No circular dependencies**  
✅ **Executable builds**: `humangl` (192KB)  

**Your CStyle system is ready for development!** 🚀
