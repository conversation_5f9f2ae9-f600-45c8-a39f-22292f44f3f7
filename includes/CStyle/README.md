# HumanGL CStyle Header System

## Overview

The CStyle header system provides a centralized, organized approach to including all necessary headers for the HumanGL project. Instead of managing multiple individual includes in each source file, you can now use a single include that brings in everything you need.

## Structure

```
includes/CStyle/
├── defines.hpp     # All #define statements and constants
├── libs.hpp        # All library includes (SDL2, OpenGL, STL)
├── enums.hpp       # All enum definitions (currently compatibility mode)
├── structs.hpp     # All struct definitions (with new utility structs)
├── humangl.hpp     # Master include file (includes all above + project headers)
└── README.md       # This documentation
```

## Usage

### For Main Application Files (like main.cpp)

Instead of multiple includes:
```cpp
// OLD WAY - Multiple includes
#define GL_SILENCE_DEPRECATION
#include <SDL2/SDL.h>
#include <SDL2/SDL_opengl.h>
#include <OpenGL/gl.h>
#include <iostream>
#include <vector>
#include <cmath>
#include "../includes/MenuTypes.hpp"
#include "../includes/MenuSystem.hpp"
#include "../includes/Matrix4.hpp"
#include "../includes/MatrixStack.hpp"
```

Use single include:
```cpp
// NEW WAY - Single include for main application
#include "../includes/CStyle/humangl.hpp"
```

### For Individual .cpp Files (Recommended Pattern)

Each .cpp file should include only its corresponding .hpp file:
```cpp
// Example: sources/MenuRenderer.cpp
#include "../includes/MenuRenderer.hpp"
#include "../includes/TextRenderer.hpp"  // Only if needed for implementation
```

The .hpp file handles all the CStyle includes:
```cpp
// Example: includes/MenuRenderer.hpp
#include "CStyle/libs.hpp"
#include "CStyle/structs.hpp"
// Forward declarations as needed
```

### Individual Component Usage

If you only need specific components:
```cpp
// Only basic libraries and defines
#include "../includes/CStyle/defines.hpp"
#include "../includes/CStyle/libs.hpp"

// Only utility structures
#include "../includes/CStyle/structs.hpp"
```

## File Contents

### defines.hpp
- OpenGL deprecation silencing
- Math constants (M_PI, etc.)
- Application constants (window size, title, etc.)
- Animation timing constants
- Color constants
- Utility macros (DEGREES_TO_RADIANS, etc.)

### libs.hpp
- SDL2 includes
- Platform-specific OpenGL includes
- Standard C++ library includes
- Additional utility libraries for future expansion

### enums.hpp
- Currently in compatibility mode (doesn't redefine existing enums)
- Additional enums for body parts, input keys, quality settings, etc.
- Future: Will contain all project enumerations

### structs.hpp
- New utility structures:
  - `Color` - RGB/RGBA color management
  - `Vector3` - 3D vector operations
  - `BodyPartSettings` - Body part customization
  - `AnimationKeyframe` - Animation data
  - `WindowSettings` - Window configuration
  - `InputState` - Input management
  - `PerformanceMetrics` - Performance tracking

### humangl.hpp
- Master include file
- Includes all CStyle components in correct order
- Includes all existing project headers
- Provides convenience macros and inline functions
- Version and build information

## Benefits

1. **Simplified Includes**: One include instead of many
2. **Consistent Dependencies**: All files use the same includes
3. **Easier Maintenance**: Update includes in one place
4. **Better Organization**: Logical separation of concerns
5. **Future-Proof**: Easy to add new dependencies
6. **Compile-Time Optimization**: Reduced redundant parsing

## Convenience Features

The master header provides several convenience features:

### Utility Functions
```cpp
float result = clamp(value, 0.0f, 1.0f);
float interpolated = lerp(start, end, 0.5f);
float smooth = smoothstep(0.0f, 1.0f, t);
```

### OpenGL Helpers
```cpp
setGLColor(Color::Red());
setGLClearColor(Color(0.2f, 0.3f, 0.4f));
glTranslatev(Vector3(1.0f, 2.0f, 3.0f));
glRotatev(45.0f, Vector3(0.0f, 1.0f, 0.0f));
```

### Constants
```cpp
// Use predefined constants instead of magic numbers
int width = HUMANGL_DEFAULT_WINDOW_WIDTH;
float fov = HUMANGL_DEFAULT_FOV;
Color skin = Color::SkinTone();
```

## Migration Guide

### For New Files
Simply include the master header:
```cpp
#include "../includes/CStyle/humangl.hpp"
```

### For Existing Files
Replace multiple includes with the single include:
```cpp
// Remove all these:
// #include <SDL2/SDL.h>
// #include <iostream>
// #include <vector>
// etc...

// Add this:
#include "../includes/CStyle/humangl.hpp"
```

## Compatibility

- **Backward Compatible**: Existing code continues to work
- **No Breaking Changes**: All existing types and functions available
- **Gradual Migration**: Can be adopted file by file
- **Cross-Platform**: Works on macOS, Windows, and Linux

## Future Enhancements

- Complete enum consolidation
- Additional utility structures
- Template-based math operations
- Memory management helpers
- Logging and debugging utilities
- Configuration management

## Examples

See the updated files for examples:
- `src/main.cpp` - Main application using master include
- `sources/MatrixStack.cpp` - Library file using master include

## Notes

- The system is designed to be compatible with existing code
- Some enums/structs are not redefined to avoid conflicts
- The master include handles all dependency ordering
- All platform-specific code is handled automatically
