#ifndef SETTINGSMENU_HPP
#define SETTINGSMENU_HPP

// Use CStyle includes for all dependencies
#include "CStyle/humangl.hpp"

class SettingsMenu : public BaseMenu {
private:
    SettingsMenuRenderer& renderer;

public:
    SettingsMenu(SettingsMenuRenderer& renderer, MouseHandler& mouseHandler, MenuInput& menuInput, int winWidth, int winHeight);
    virtual ~SettingsMenu() = default;

    // Implement pure virtual methods from BaseMenu
    void initializeButtons() override;
    void render() override;
    MenuAction handleButtonClick(int buttonIndex) override;
};

#endif // SETTINGSMENU_HPP
