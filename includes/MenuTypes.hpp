#ifndef MENUTYPES_HPP
#define MENUTYPES_HPP

// Use CStyle includes for all dependencies
#include "CStyle/humangl.hpp"

// Note: AppState and MenuAction enums are now defined in CStyle/enums.hpp
// This file now only contains the MenuButton struct for compatibility

// Menu button structure (used by menu system)
struct MenuButton {
    float x, y, width, height;
    std::string text;
    bool hovered;
    
    MenuButton(float px, float py, float w, float h, const std::string& txt) 
        : x(px), y(py), width(w), height(h), text(txt), hovered(false) {}
    
    bool isPointInside(float px, float py) const {
        return px >= x && px <= x + width && py >= y && py <= y + height;
    }
};

#endif // MENUTYPES_HPP
