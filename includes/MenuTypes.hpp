#ifndef MENUTYPES_HPP
#define MENUTYPES_HPP

#include <string>

// Enum definitions (shared between main.cpp and menu system)
enum AppState {
    MAIN_MENU,
    SIMULATION,
    SETTINGS,
    CREDITS,
    INSTRUCTIONS
};

enum MenuAction {
    MENU_ACTION_NONE,
    MENU_ACTION_START_SIMULATION,
    MENU_ACTION_SETTINGS,
    MENU_ACTION_CREDITS,
    MENU_ACTION_INSTRUCTIONS,
    MENU_ACTION_EXIT,
    MENU_ACTION_BACK_TO_MENU,
    MENU_ACTION_RETURN_TO_MENU
};

// Menu button structure (used by menu system)
struct MenuButton {
    float x, y, width, height;
    std::string text;
    bool hovered;
    
    MenuButton(float px, float py, float w, float h, const std::string& txt) 
        : x(px), y(py), width(w), height(h), text(txt), hovered(false) {}
    
    bool isPointInside(float px, float py) const {
        return px >= x && px <= x + width && py >= y && py <= y + height;
    }
};

#endif // MENUTYPES_HPP
