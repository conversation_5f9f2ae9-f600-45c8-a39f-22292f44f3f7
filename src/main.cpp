#define GL_SILENCE_DEPRECATION
#include <SDL2/SDL.h>
#include <SDL2/SDL_opengl.h>

// Platform-specific OpenGL includes
#ifdef __APPLE__
    #include <OpenGL/gl.h>
    #include <OpenGL/glu.h>
#elif defined(_WIN32)
    #include <GL/gl.h>
    #include <GL/glu.h>
#else
    // Linux
    #include <GL/gl.h>
    #include <GL/glu.h>
#endif

#include <iostream>
#include <vector>
#include <cmath>
#include <string>

// Include menu system headers
#include "../includes/MenuTypes.hpp"
#include "../includes/MenuSystem.hpp"

// Include matrix classes
#include "../includes/Matrix4.hpp"
#include "../includes/MatrixStack.hpp"

class Application {
private:
    SDL_Window* window;
    SDL_GLContext glContext;
    bool running;
    int windowWidth;
    int windowHeight;

    // Menu system
    MenuSystem menuSystem;
    AppState currentState;

    // Matrix stack for hierarchical transformations
    MatrixStack matrixStack;

    // Body part rotations
    float torsoRotationY;
    float headRotationX;
    float headRotationY;

    // Arm rotations (left and right)
    float leftUpperArmX, leftUpperArmZ;
    float leftForearmX;
    float rightUpperArmX, rightUpperArmZ;
    float rightForearmX;

    // Leg rotations (left and right)
    float leftThighX;
    float leftLowerLegX;
    float rightThighX;
    float rightLowerLegX;

    // Animation modes
    enum AnimationMode {
        NONE,
        WALKING,
        JUMPING,
        DANCING,
        KUNGFU
    };

    AnimationMode currentAnimation;
    float animationTime;
    float jumpHeight;

    // Keyboard state
    const Uint8* keyboardState;

public:
    Application() : window(nullptr), glContext(nullptr), running(false),
                   windowWidth(800), windowHeight(600),
                   currentState(MAIN_MENU),
                   torsoRotationY(0.0f), headRotationX(0.0f), headRotationY(0.0f),
                   leftUpperArmX(0.0f), leftUpperArmZ(0.0f), leftForearmX(0.0f),
                   rightUpperArmX(0.0f), rightUpperArmZ(0.0f), rightForearmX(0.0f),
                   leftThighX(0.0f), leftLowerLegX(0.0f),
                   rightThighX(0.0f), rightLowerLegX(0.0f),
                   currentAnimation(NONE), animationTime(0.0f), jumpHeight(0.0f),
                   keyboardState(nullptr) {}

    ~Application() {
        cleanup();
    }

    bool initialize() {
        if (SDL_Init(SDL_INIT_VIDEO) < 0) {
            std::cerr << "SDL could not initialize! SDL_Error: " << SDL_GetError() << std::endl;
            return false;
        }

        SDL_GL_SetAttribute(SDL_GL_CONTEXT_MAJOR_VERSION, 2);
        SDL_GL_SetAttribute(SDL_GL_CONTEXT_MINOR_VERSION, 1);
        SDL_GL_SetAttribute(SDL_GL_DOUBLEBUFFER, 1);
        SDL_GL_SetAttribute(SDL_GL_DEPTH_SIZE, 24);

        window = SDL_CreateWindow("HumanGL - Skeletal Animation",
                                SDL_WINDOWPOS_UNDEFINED, SDL_WINDOWPOS_UNDEFINED,
                                windowWidth, windowHeight,
                                SDL_WINDOW_OPENGL | SDL_WINDOW_SHOWN | SDL_WINDOW_RESIZABLE);

        if (window == nullptr) {
            std::cerr << "Window could not be created! SDL_Error: " << SDL_GetError() << std::endl;
            return false;
        }

        glContext = SDL_GL_CreateContext(window);
        if (glContext == nullptr) {
            std::cerr << "OpenGL context could not be created! SDL_Error: " << SDL_GetError() << std::endl;
            return false;
        }

        if (SDL_GL_SetSwapInterval(1) < 0) {
            std::cerr << "Warning: Unable to set VSync! SDL_Error: " << SDL_GetError() << std::endl;
        }

        glEnable(GL_DEPTH_TEST);
        glClearColor(0.2f, 0.3f, 0.4f, 1.0f);  // Nice blue-gray background for menus
        glViewport(0, 0, windowWidth, windowHeight);

        setupPerspective();

        keyboardState = SDL_GetKeyboardState(nullptr);

        // Initialize menu system
        if (!menuSystem.initialize(windowWidth, windowHeight)) {
            std::cerr << "Failed to initialize menu system" << std::endl;
            return false;
        }

        running = true;
        return true;
    }

    void cleanup() {
        menuSystem.cleanup();
        if (glContext) {
            SDL_GL_DeleteContext(glContext);
            glContext = nullptr;
        }
        if (window) {
            SDL_DestroyWindow(window);
            window = nullptr;
        }
        SDL_Quit();
    }

    void setupPerspective() {
        glMatrixMode(GL_PROJECTION);
        glLoadIdentity();

        float aspectRatio = static_cast<float>(windowWidth) / static_cast<float>(windowHeight);
        float fov = 45.0f;
        float nearPlane = 0.1f;
        float farPlane = 100.0f;

        float top = nearPlane * tanf(fov * M_PI / 360.0f);
        float bottom = -top;
        float right = top * aspectRatio;
        float left = -right;

        glFrustum(left, right, bottom, top, nearPlane, farPlane);
        glMatrixMode(GL_MODELVIEW);
        glEnable(GL_DEPTH_TEST);
    }

    void drawColoredCube(float r, float g, float b) {
        // Ensure color is set properly
        glColor3f(r, g, b);

        glBegin(GL_QUADS);

        // Front face
        glVertex3f(-0.5f, -0.5f,  0.5f);
        glVertex3f( 0.5f, -0.5f,  0.5f);
        glVertex3f( 0.5f,  0.5f,  0.5f);
        glVertex3f(-0.5f,  0.5f,  0.5f);

        // Back face
        glVertex3f(-0.5f, -0.5f, -0.5f);
        glVertex3f(-0.5f,  0.5f, -0.5f);
        glVertex3f( 0.5f,  0.5f, -0.5f);
        glVertex3f( 0.5f, -0.5f, -0.5f);

        // Top face
        glVertex3f(-0.5f,  0.5f, -0.5f);
        glVertex3f(-0.5f,  0.5f,  0.5f);
        glVertex3f( 0.5f,  0.5f,  0.5f);
        glVertex3f( 0.5f,  0.5f, -0.5f);

        // Bottom face
        glVertex3f(-0.5f, -0.5f, -0.5f);
        glVertex3f( 0.5f, -0.5f, -0.5f);
        glVertex3f( 0.5f, -0.5f,  0.5f);
        glVertex3f(-0.5f, -0.5f,  0.5f);

        // Right face
        glVertex3f( 0.5f, -0.5f, -0.5f);
        glVertex3f( 0.5f,  0.5f, -0.5f);
        glVertex3f( 0.5f,  0.5f,  0.5f);
        glVertex3f( 0.5f, -0.5f,  0.5f);

        // Left face
        glVertex3f(-0.5f, -0.5f, -0.5f);
        glVertex3f(-0.5f, -0.5f,  0.5f);
        glVertex3f(-0.5f,  0.5f,  0.5f);
        glVertex3f(-0.5f,  0.5f, -0.5f);

        glEnd();
    }



    void drawTorso() {
        glPushMatrix();
        glScalef(1.0f, 1.5f, 0.5f);
        drawColoredCube(0.2f, 0.4f, 0.8f);  // Blue shirt
        glPopMatrix();
    }

    void drawNeck() {
        glPushMatrix();
        glTranslatef(0.0f, 0.9f, 0.0f);
        glScalef(0.3f, 0.3f, 0.3f);
        drawColoredCube(0.8f, 0.6f, 0.4f);  // Skin color for neck
        glPopMatrix();
    }

    void drawHead() {
        glPushMatrix();
        glTranslatef(0.0f, 1.4f, 0.0f);
        glRotatef(headRotationX, 1.0f, 0.0f, 0.0f);
        glRotatef(headRotationY, 0.0f, 1.0f, 0.0f);
        glScalef(0.6f, 0.6f, 0.6f);
        drawColoredCube(0.8f, 0.6f, 0.4f);  // Skin color for head
        glPopMatrix();
    }

    void drawEyes() {
        glPushMatrix();
        glTranslatef(0.0f, 1.4f, 0.0f);
        glRotatef(headRotationX, 1.0f, 0.0f, 0.0f);
        glRotatef(headRotationY, 0.0f, 1.0f, 0.0f);

        // Left eye
        glPushMatrix();
        glTranslatef(-0.15f, 0.1f, 0.31f);
        glScalef(0.1f, 0.1f, 0.1f);
        drawColoredCube(0.0f, 0.0f, 0.0f);  // Black eyes
        glPopMatrix();

        // Right eye
        glPushMatrix();
        glTranslatef(0.15f, 0.1f, 0.31f);
        glScalef(0.1f, 0.1f, 0.1f);
        drawColoredCube(0.0f, 0.0f, 0.0f);  // Black eyes
        glPopMatrix();

        glPopMatrix();
    }

    void drawLeftShoulder() {
        glPushMatrix();
        glTranslatef(-0.7f, 0.5f, 0.0f);
        glScalef(0.3f, 0.3f, 0.3f);
        drawColoredCube(0.2f, 0.4f, 0.8f);  // Blue shirt color for shoulders
        glPopMatrix();
    }

    void drawRightShoulder() {
        glPushMatrix();
        glTranslatef(0.7f, 0.5f, 0.0f);
        glScalef(0.3f, 0.3f, 0.3f);
        drawColoredCube(0.2f, 0.4f, 0.8f);  // Blue shirt color for shoulders
        glPopMatrix();
    }

    void drawLeftArm() {
        // Draw the entire left arm as a connected hierarchy
        glPushMatrix();
        glTranslatef(-0.7f, 0.5f, 0.0f);
        glRotatef(leftUpperArmX, 1.0f, 0.0f, 0.0f);
        glRotatef(leftUpperArmZ, 0.0f, 0.0f, 1.0f);

        // Draw upper arm (positioned to connect with shoulder)
        glPushMatrix();
        glTranslatef(0.0f, -0.4f, 0.0f);  // Move up to connect with shoulder
        glScalef(0.25f, 0.8f, 0.25f);
        drawColoredCube(0.2f, 0.4f, 0.8f);  // Blue shirt sleeves
        glPopMatrix();

        // Move to elbow and draw forearm (no gap)
        glTranslatef(0.0f, -0.8f, 0.0f);  // Position at end of upper arm
        glRotatef(leftForearmX, 1.0f, 0.0f, 0.0f);
        glTranslatef(0.0f, -0.3f, 0.0f);  // Move down for forearm center
        glScalef(0.2f, 0.6f, 0.2f);
        drawColoredCube(0.8f, 0.6f, 0.4f);  // Skin color for forearms

        glPopMatrix();
    }

    void drawRightArm() {
        // Draw the entire right arm as a connected hierarchy
        glPushMatrix();
        glTranslatef(0.7f, 0.5f, 0.0f);
        glRotatef(rightUpperArmX, 1.0f, 0.0f, 0.0f);
        glRotatef(rightUpperArmZ, 0.0f, 0.0f, 1.0f);

        // Draw upper arm (positioned to connect with shoulder)
        glPushMatrix();
        glTranslatef(0.0f, -0.4f, 0.0f);  // Move up to connect with shoulder
        glScalef(0.25f, 0.8f, 0.25f);
        drawColoredCube(0.2f, 0.4f, 0.8f);  // Blue shirt sleeves
        glPopMatrix();

        // Move to elbow and draw forearm (no gap)
        glTranslatef(0.0f, -0.8f, 0.0f);  // Position at end of upper arm
        glRotatef(rightForearmX, 1.0f, 0.0f, 0.0f);
        glTranslatef(0.0f, -0.3f, 0.0f);  // Move down for forearm center
        glScalef(0.2f, 0.6f, 0.2f);
        drawColoredCube(0.8f, 0.6f, 0.4f);  // Skin color for forearms

        glPopMatrix();
    }

    void drawLeftLeg() {
        // Draw the entire left leg as a connected hierarchy
        glPushMatrix();
        glTranslatef(-0.3f, -0.75f, 0.0f);
        glRotatef(leftThighX, 1.0f, 0.0f, 0.0f);

        // Draw thigh (positioned to connect with torso)
        glPushMatrix();
        glTranslatef(0.0f, -0.4f, 0.0f);  // Move up to connect with torso
        glScalef(0.3f, 0.8f, 0.3f);
        drawColoredCube(0.1f, 0.1f, 0.3f);  // Dark blue pants
        glPopMatrix();

        // Move to knee and draw lower leg (no gap)
        glTranslatef(0.0f, -0.8f, 0.0f);  // Position at end of thigh
        glRotatef(leftLowerLegX, 1.0f, 0.0f, 0.0f);
        glTranslatef(0.0f, -0.3f, 0.0f);  // Move down for lower leg center
        glScalef(0.25f, 0.6f, 0.25f);
        drawColoredCube(0.8f, 0.6f, 0.4f);  // Skin color for lower legs

        glPopMatrix();
    }

    void drawRightLeg() {
        // Draw the entire right leg as a connected hierarchy
        glPushMatrix();
        glTranslatef(0.3f, -0.75f, 0.0f);
        glRotatef(rightThighX, 1.0f, 0.0f, 0.0f);

        // Draw thigh (positioned to connect with torso)
        glPushMatrix();
        glTranslatef(0.0f, -0.4f, 0.0f);  // Move up to connect with torso
        glScalef(0.3f, 0.8f, 0.3f);
        drawColoredCube(0.1f, 0.1f, 0.3f);  // Dark blue pants
        glPopMatrix();

        // Move to knee and draw lower leg (no gap)
        glTranslatef(0.0f, -0.8f, 0.0f);  // Position at end of thigh
        glRotatef(rightLowerLegX, 1.0f, 0.0f, 0.0f);
        glTranslatef(0.0f, -0.3f, 0.0f);  // Move down for lower leg center
        glScalef(0.25f, 0.6f, 0.25f);
        drawColoredCube(0.8f, 0.6f, 0.4f);  // Skin color for lower legs

        glPopMatrix();
    }

    void handleEvents() {
        SDL_Event event;
        while (SDL_PollEvent(&event)) {
            // Handle quit event
            if (event.type == SDL_QUIT) {
                running = false;
                continue;
            }

            // Handle window resize
            if (event.type == SDL_WINDOWEVENT && event.window.event == SDL_WINDOWEVENT_RESIZED) {
                windowWidth = event.window.data1;
                windowHeight = event.window.data2;
                glViewport(0, 0, windowWidth, windowHeight);
                setupPerspective();
                menuSystem.updateWindowSize(windowWidth, windowHeight);
                continue;
            }

            // Let menu system handle the event
            MenuAction action = menuSystem.handleEvent(event);

            // Process menu actions
            switch (action) {
                case MENU_ACTION_START_SIMULATION:
                    currentState = SIMULATION;
                    menuSystem.setState(SIMULATION);
                    break;
                case MENU_ACTION_SETTINGS:
                    currentState = SETTINGS;
                    break;
                case MENU_ACTION_CREDITS:
                    currentState = CREDITS;
                    break;
                case MENU_ACTION_EXIT:
                    running = false;
                    break;
                case MENU_ACTION_BACK_TO_MENU:
                case MENU_ACTION_RETURN_TO_MENU:
                    currentState = MAIN_MENU;
                    menuSystem.setState(MAIN_MENU);
                    break;
                default:
                    break;
            }

            // Handle simulation-specific events
            if (currentState == SIMULATION && event.type == SDL_KEYDOWN) {
                if (event.key.keysym.sym == SDLK_SPACE) {
                    // Toggle walking mode
                    if (currentAnimation == WALKING) {
                        currentAnimation = NONE;
                        // Reset limb positions
                        leftThighX = rightThighX = 0.0f;
                        leftLowerLegX = rightLowerLegX = 0.0f;
                        leftUpperArmX = rightUpperArmX = 0.0f;
                        leftUpperArmZ = rightUpperArmZ = 0.0f;
                        leftForearmX = rightForearmX = 0.0f;
                        animationTime = 0.0f;
                    } else if (currentAnimation == NONE) {
                        currentAnimation = WALKING;
                        animationTime = 0.0f;
                    }
                }
                if (event.key.keysym.sym == SDLK_p) {
                    if (currentAnimation == NONE) {
                        currentAnimation = JUMPING;
                        animationTime = 0.0f;
                        jumpHeight = 0.0f;
                    }
                }
                if (event.key.keysym.sym == SDLK_j) {  // J for dancing
                    if (currentAnimation == DANCING) {
                        currentAnimation = NONE;
                        // Reset limb positions
                        leftThighX = rightThighX = 0.0f;
                        leftLowerLegX = rightLowerLegX = 0.0f;
                        leftUpperArmX = rightUpperArmX = 0.0f;
                        leftUpperArmZ = rightUpperArmZ = 0.0f;
                        leftForearmX = rightForearmX = 0.0f;
                        headRotationX = headRotationY = 0.0f;
                        animationTime = 0.0f;
                    } else if (currentAnimation == NONE) {
                        currentAnimation = DANCING;
                        animationTime = 0.0f;
                    }
                }
                if (event.key.keysym.sym == SDLK_k) {  // K for kung fu
                    if (currentAnimation == KUNGFU) {
                        currentAnimation = NONE;
                        // Reset limb positions
                        leftThighX = rightThighX = 0.0f;
                        leftLowerLegX = rightLowerLegX = 0.0f;
                        leftUpperArmX = rightUpperArmX = 0.0f;
                        leftUpperArmZ = rightUpperArmZ = 0.0f;
                        leftForearmX = rightForearmX = 0.0f;
                        animationTime = 0.0f;
                    } else if (currentAnimation == NONE) {
                        currentAnimation = KUNGFU;
                        animationTime = 0.0f;
                    }
                }
            }
        }

        // Update menu system
        MenuAction action = menuSystem.update();
        switch (action) {
            case MENU_ACTION_EXIT:
                running = false;
                break;
            case MENU_ACTION_RETURN_TO_MENU:
                currentState = MAIN_MENU;
                menuSystem.setState(MAIN_MENU);
                break;
            default:
                break;
        }
    }

    void updateAnimations() {
        if (currentAnimation == NONE) return;

        animationTime += 0.05f;  // Increment animation time

        switch (currentAnimation) {
            case WALKING:
                // Simple walking animation
                leftThighX = 30.0f * sinf(animationTime * 2.0f);
                rightThighX = -30.0f * sinf(animationTime * 2.0f);
                leftLowerLegX = fmaxf(0.0f, -40.0f * sinf(animationTime * 2.0f));
                rightLowerLegX = fmaxf(0.0f, 40.0f * sinf(animationTime * 2.0f));

                // Arm swing (opposite to legs)
                leftUpperArmX = -20.0f * sinf(animationTime * 2.0f);
                rightUpperArmX = 20.0f * sinf(animationTime * 2.0f);
                break;

            case JUMPING:
                // Simple jump animation
                if (animationTime < 1.0f) {
                    jumpHeight = 2.0f * sinf(animationTime * M_PI);
                    leftThighX = rightThighX = -45.0f * sinf(animationTime * M_PI);
                    leftLowerLegX = rightLowerLegX = 90.0f * sinf(animationTime * M_PI);
                    leftUpperArmX = rightUpperArmX = -90.0f * sinf(animationTime * M_PI);
                } else {
                    // Jump finished, reset
                    currentAnimation = NONE;
                    jumpHeight = 0.0f;
                    leftThighX = rightThighX = 0.0f;
                    leftLowerLegX = rightLowerLegX = 0.0f;
                    leftUpperArmX = rightUpperArmX = 0.0f;
                    animationTime = 0.0f;
                }
                break;

            case DANCING:
                // Disco dancing animation
                leftUpperArmX = 45.0f * sinf(animationTime * 3.0f);
                rightUpperArmX = 45.0f * sinf(animationTime * 3.0f + M_PI);
                leftUpperArmZ = 30.0f * cosf(animationTime * 2.0f);
                rightUpperArmZ = -30.0f * cosf(animationTime * 2.0f);

                headRotationY = 20.0f * sinf(animationTime * 1.5f);
                torsoRotationY = 10.0f * sinf(animationTime * 1.0f);

                leftThighX = 15.0f * sinf(animationTime * 4.0f);
                rightThighX = 15.0f * sinf(animationTime * 4.0f + M_PI);
                break;

            case KUNGFU:
                // Kung fu fighting animation
                if (fmodf(animationTime, 2.0f) < 1.0f) {
                    // Punch sequence
                    leftUpperArmX = -90.0f;
                    leftUpperArmZ = 45.0f;
                    rightUpperArmX = 0.0f;
                    rightUpperArmZ = -90.0f;
                } else {
                    // Kick sequence
                    leftUpperArmX = 45.0f;
                    leftUpperArmZ = 0.0f;
                    rightUpperArmX = 45.0f;
                    rightUpperArmZ = 0.0f;
                    leftThighX = 90.0f;
                    leftLowerLegX = -45.0f;
                }
                break;

            default:
                break;
        }
    }

    void handleKeyboardInput() {
        if (!keyboardState) return;

        // Torso rotation
        if (keyboardState[SDL_SCANCODE_A]) {
            torsoRotationY -= 2.0f;
        }
        if (keyboardState[SDL_SCANCODE_D]) {
            torsoRotationY += 2.0f;
        }

        // Head rotation
        if (keyboardState[SDL_SCANCODE_W]) {
            headRotationX = fmaxf(headRotationX - 2.0f, -45.0f);
        }
        if (keyboardState[SDL_SCANCODE_S]) {
            headRotationX = fminf(headRotationX + 2.0f, 45.0f);
        }
        if (keyboardState[SDL_SCANCODE_Q]) {
            headRotationY = fmaxf(headRotationY - 2.0f, -60.0f);
        }
        if (keyboardState[SDL_SCANCODE_E]) {
            headRotationY = fminf(headRotationY + 2.0f, 60.0f);
        }

        // Left arm controls (1, 2, 3)
        if (keyboardState[SDL_SCANCODE_1]) {
            leftUpperArmX = fmaxf(leftUpperArmX - 2.0f, -180.0f);
        }
        if (keyboardState[SDL_SCANCODE_2]) {
            leftUpperArmZ = fmaxf(leftUpperArmZ - 2.0f, -90.0f);
        }
        if (keyboardState[SDL_SCANCODE_3]) {
            leftForearmX = fmaxf(leftForearmX - 2.0f, -135.0f);
        }

        // Right arm controls (4, 5, 6)
        if (keyboardState[SDL_SCANCODE_4]) {
            rightUpperArmX = fmaxf(rightUpperArmX - 2.0f, -180.0f);
        }
        if (keyboardState[SDL_SCANCODE_5]) {
            rightUpperArmZ = fminf(rightUpperArmZ + 2.0f, 90.0f);
        }
        if (keyboardState[SDL_SCANCODE_6]) {
            rightForearmX = fmaxf(rightForearmX - 2.0f, -135.0f);
        }

        // Left leg controls (7, 8)
        if (keyboardState[SDL_SCANCODE_7]) {
            leftThighX = fmaxf(leftThighX - 2.0f, -90.0f);
        }
        if (keyboardState[SDL_SCANCODE_8]) {
            leftLowerLegX = fmaxf(leftLowerLegX - 2.0f, -135.0f);
        }

        // Right leg controls (9, 0)
        if (keyboardState[SDL_SCANCODE_9]) {
            rightThighX = fmaxf(rightThighX - 2.0f, -90.0f);
        }
        if (keyboardState[SDL_SCANCODE_0]) {
            rightLowerLegX = fmaxf(rightLowerLegX - 2.0f, -135.0f);
        }
    }

    void render() {
        // Render based on current state
        if (currentState == SIMULATION) {
            renderSimulation();
        } else {
            // Let menu system handle menu rendering
            menuSystem.render();
        }

        // Swap buffers
        SDL_GL_SwapWindow(window);
    }

    void renderSimulation() {
        // Set background color to dark blue/black
        glClearColor(0.1f, 0.1f, 0.2f, 1.0f);

        // Clear the screen
        glClear(GL_COLOR_BUFFER_BIT | GL_DEPTH_BUFFER_BIT);

        // Enable depth testing and disable lighting to ensure colors show properly
        glEnable(GL_DEPTH_TEST);
        glDisable(GL_LIGHTING);  // Make sure lighting doesn't interfere with colors
        glEnable(GL_CULL_FACE);
        glCullFace(GL_BACK);

        // Setup 3D rendering
        glMatrixMode(GL_PROJECTION);
        glLoadIdentity();

        float aspectRatio = static_cast<float>(windowWidth) / static_cast<float>(windowHeight);
        float fov = 45.0f;
        float nearPlane = 0.1f;
        float farPlane = 100.0f;

        float top = nearPlane * tanf(fov * static_cast<float>(M_PI) / 360.0f);
        float bottom = -top;
        float right = top * aspectRatio;
        float left = -right;

        glFrustum(left, right, bottom, top, nearPlane, farPlane);
        glMatrixMode(GL_MODELVIEW);
        glLoadIdentity();

        // Move camera back to see the figure
        glTranslatef(0.0f, 0.0f, -8.0f);

        // Apply jump height (affects entire body)
        glTranslatef(0.0f, jumpHeight, 0.0f);

        // Apply torso rotation (this affects all body parts)
        glRotatef(torsoRotationY, 0.0f, 1.0f, 0.0f);

        // Draw body parts in hierarchical order
        drawTorso();           // Base of hierarchy
        drawNeck();            // Connected to torso
        drawHead();            // Connected to neck
        drawEyes();            // Eyes on the head (shows front direction)

        // Draw shoulder joints
        drawLeftShoulder();    // Left shoulder joint
        drawRightShoulder();   // Right shoulder joint

        // Draw arms (connected to shoulders)
        drawLeftArm();         // Left arm (upper arm + forearm connected)
        drawRightArm();        // Right arm (upper arm + forearm connected)

        // Draw legs (connected to torso)
        drawLeftLeg();         // Left leg (thigh + lower leg connected)
        drawRightLeg();        // Right leg (thigh + lower leg connected)

        // Disable states we enabled
        glDisable(GL_CULL_FACE);
    }

    void run() {
        if (!initialize()) {
            return;
        }

        std::cout << "=== HumanGL - Skeletal Animation Demo ===" << std::endl;
        std::cout << "Use the menu to navigate the application." << std::endl;
        std::cout << "In simulation mode:" << std::endl;
        std::cout << "  M: Return to main menu" << std::endl;
        std::cout << "  SPACE: Toggle walking animation" << std::endl;
        std::cout << "  P: Jump (single jump)" << std::endl;
        std::cout << "  J: Toggle disco dancing" << std::endl;
        std::cout << "  K: Toggle kung fu fighting" << std::endl;
        std::cout << "  A/D: Rotate torso, W/S: Head up/down, Q/E: Head left/right" << std::endl;
        std::cout << "  1-3: Left arm, 4-6: Right arm, 7-8: Left leg, 9-0: Right leg" << std::endl;
        std::cout << "ESC: Exit application" << std::endl;

        while (running) {
            handleEvents();

            // Only update animations and handle keyboard input in simulation mode
            if (currentState == SIMULATION) {
                updateAnimations();        // Update all animations
                handleKeyboardInput();     // Handle continuous keyboard input
            }

            render();

            // Small delay to prevent excessive CPU usage
            SDL_Delay(16); // ~60 FPS
        }

        cleanup();
    }
};

int main(int argc, char* argv[]) {
    (void)argc;  // Suppress unused parameter warning
    (void)argv;  // Suppress unused parameter warning

    Application app;
    app.run();
    return 0;
}
