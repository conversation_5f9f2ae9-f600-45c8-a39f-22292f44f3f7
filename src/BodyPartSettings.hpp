#pragma once

struct BodyPartSettings {
    float scaleX, scaleY, scaleZ;
    float colorR, colorG, colorB;
    
    BodyPartSettings() : scaleX(1.0f), scaleY(1.0f), scaleZ(1.0f), 
                       colorR(0.8f), colorG(0.6f), colorB(0.4f) {}
    
    BodyPartSettings(float r, float g, float b) : scaleX(1.0f), scaleY(1.0f), scaleZ(1.0f),
                                                colorR(r), colorG(g), colorB(b) {}
};
