#pragma once

#include <OpenGL/gl.h>
#include <cmath>
#include <stack>

class MatrixStack {
private:
    struct Matrix4x4 {
        float m[16];
        
        Matrix4x4() {
            // Initialize as identity matrix
            for (int i = 0; i < 16; i++) {
                m[i] = 0.0f;
            }
            m[0] = m[5] = m[10] = m[15] = 1.0f;
        }
        
        Matrix4x4 operator*(const Matrix4x4& other) const {
            Matrix4x4 result;
            for (int i = 0; i < 4; i++) {
                for (int j = 0; j < 4; j++) {
                    result.m[i * 4 + j] = 0.0f;
                    for (int k = 0; k < 4; k++) {
                        result.m[i * 4 + j] += m[i * 4 + k] * other.m[k * 4 + j];
                    }
                }
            }
            return result;
        }
    };
    
    std::stack<Matrix4x4> matrixStack;
    Matrix4x4 currentMatrix;
    
    float degreesToRadians(float degrees);

public:
    MatrixStack();
    
    void loadIdentity();
    void pushMatrix();
    void popMatrix();
    
    void translate(float x, float y, float z);
    void rotateX(float degrees);
    void rotateY(float degrees);
    void rotateZ(float degrees);
    void scale(float x, float y, float z);
    
    void applyToOpenGL();
};
