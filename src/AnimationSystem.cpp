#include "AnimationSystem.hpp"
#include "Application.hpp"
#include <cmath>

AnimationSystem::AnimationSystem(Application* app) 
    : application(app), currentAnimation(NONE), animationTime(0.0f), jumpHeight(0.0f) {
}

AnimationSystem::~AnimationSystem() {
}

void AnimationSystem::setAnimation(AnimationMode mode) {
    if (currentAnimation != mode) {
        currentAnimation = mode;
        animationTime = 0.0f;
        jumpHeight = 0.0f;
    }
}

void AnimationSystem::resetAnimation() {
    currentAnimation = NONE;
    animationTime = 0.0f;
    jumpHeight = 0.0f;
}

void AnimationSystem::update() {
    switch (currentAnimation) {
        case WALKING:
            updateWalkingAnimation();
            break;
        case JUMPING:
            updateJumpingAnimation();
            break;
        case DANCING:
            updateDancingAnimation();
            break;
        case KUNGFU:
            updateKungFuAnimation();
            break;
        default:
            break;
    }
}

void AnimationSystem::updateWalkingAnimation() {
    animationTime += 0.1f;  // Animation speed
    
    // Walking cycle using sine waves for natural movement
    float legSwing = sinf(animationTime) * 30.0f;      // Leg swing amplitude
    
    // More natural arm swing with independent upper arm and forearm movement
    float armSwingPhase = animationTime + M_PI;  // Arms opposite to legs
    float leftArmSwing = sinf(armSwingPhase) * 25.0f;   // Left arm swing
    float rightArmSwing = sinf(armSwingPhase + M_PI) * 25.0f;  // Right arm swing (opposite)
    
    // Elbow bending during arm swing - forearms bend more when arms swing back
    float leftElbowBend = (sinf(armSwingPhase + M_PI_2) + 1.0f) * 20.0f;  // 0-40 degrees
    float rightElbowBend = (sinf(armSwingPhase + M_PI_2 + M_PI) + 1.0f) * 20.0f;
    
    // Slight outward arm movement for natural walking
    float armOutward = sinf(animationTime * 2.0f) * 5.0f;
    
    // Fixed knee flexion - knees bend forward (positive rotation) during swing
    float leftKneeFlexion = (sinf(animationTime * 2.0f) + 1.0f) * 15.0f;   // Left knee
    float rightKneeFlexion = (sinf(animationTime * 2.0f + M_PI) + 1.0f) * 15.0f;  // Right knee (offset)
    
    // Apply animations to application's body part rotations
    application->leftThighX = legSwing;
    application->rightThighX = -legSwing;
    
    // Knee flexion during swing phase - FIXED: positive rotation (forward bend)
    application->leftLowerLegX = leftKneeFlexion;   // Positive = forward bend (natural)
    application->rightLowerLegX = rightKneeFlexion; // Positive = forward bend (natural)
    
    // Natural arm movement - upper arms swing, forearms bend independently
    application->leftUpperArmX = leftArmSwing;      // Forward/back swing
    application->rightUpperArmX = rightArmSwing;    // Forward/back swing
    application->leftUpperArmZ = armOutward;        // Slight outward movement
    application->rightUpperArmZ = -armOutward;      // Slight outward movement
    
    // Independent forearm movement - bend more when arm swings back
    application->leftForearmX = -leftElbowBend;     // Negative = bend (elbow flexion)
    application->rightForearmX = -rightElbowBend;   // Negative = bend (elbow flexion)
    
    // Reset animation time to prevent overflow
    if (animationTime > 2.0f * M_PI) {
        animationTime = 0.0f;
    }
}

void AnimationSystem::updateJumpingAnimation() {
    animationTime += 0.15f;  // Jump animation speed
    
    // Jump cycle: crouch -> jump -> land
    if (animationTime < M_PI) {
        // Jumping phase (0 to π)
        jumpHeight = sinf(animationTime) * 2.0f;  // Height curve
        
        // Crouch at start, extend at peak
        float jumpPhase = animationTime / M_PI;
        float legBend = (1.0f - jumpPhase) * 45.0f;  // Start crouched, extend
        
        // More natural arm movement during jump
        float armRaise = jumpPhase * 70.0f;          // Upper arms go up during jump
        float forearmExtend = jumpPhase * 30.0f;     // Forearms extend during jump
        float armSpread = jumpPhase * 25.0f;         // Arms spread outward
        
        // Leg positioning for jump
        application->leftThighX = -legBend * 0.5f;   // Slight forward lean
        application->rightThighX = -legBend * 0.5f;
        application->leftLowerLegX = legBend;        // Knees bend forward
        application->rightLowerLegX = legBend;
        
        // Natural arm movement - upper arms and forearms move independently
        application->leftUpperArmX = -armRaise;      // Upper arms swing up and back
        application->rightUpperArmX = -armRaise;
        application->leftUpperArmZ = armSpread;      // Arms spread outward for balance
        application->rightUpperArmZ = -armSpread;
        
        // Forearms extend during jump (opposite of elbow bending)
        application->leftForearmX = forearmExtend;   // Positive = extend (straighten elbow)
        application->rightForearmX = forearmExtend;  // Positive = extend (straighten elbow)
        
    } else {
        // Landing and reset
        currentAnimation = NONE;
        animationTime = 0.0f;
        jumpHeight = 0.0f;
        
        // Reset limb positions
        application->leftThighX = application->rightThighX = 0.0f;
        application->leftLowerLegX = application->rightLowerLegX = 0.0f;
        application->leftUpperArmX = application->rightUpperArmX = 0.0f;
        application->leftUpperArmZ = application->rightUpperArmZ = 0.0f;
        application->leftForearmX = application->rightForearmX = 0.0f;
    }
}

void AnimationSystem::updateDancingAnimation() {
    animationTime += 0.12f;  // Dance animation speed

    // Disco dance moves with multiple body parts
    float beat = animationTime * 2.0f;  // Faster beat
    float slowBeat = animationTime * 0.5f;  // Slower movements

    // Head bobbing
    application->headRotationX = sinf(beat * 4.0f) * 10.0f;
    application->headRotationY = sinf(beat * 2.0f) * 15.0f;

    // Arm disco moves - alternating high and low
    application->leftUpperArmX = sinf(beat) * 60.0f - 30.0f;  // Big arm swings
    application->rightUpperArmX = sinf(beat + M_PI) * 60.0f - 30.0f;  // Opposite
    application->leftUpperArmZ = sinf(beat * 1.5f) * 45.0f;   // Side to side
    application->rightUpperArmZ = sinf(beat * 1.5f + M_PI) * 45.0f;

    // Forearm pointing and gestures
    application->leftForearmX = sinf(beat * 3.0f) * 40.0f - 20.0f;
    application->rightForearmX = sinf(beat * 3.0f + M_PI) * 40.0f - 20.0f;

    // Hip swaying with legs
    application->leftThighX = sinf(slowBeat) * 20.0f;
    application->rightThighX = sinf(slowBeat + M_PI) * 20.0f;

    // Knee bending to the beat
    application->leftLowerLegX = (sinf(beat * 2.0f) + 1.0f) * 25.0f;
    application->rightLowerLegX = (sinf(beat * 2.0f + M_PI) + 1.0f) * 25.0f;

    // Reset animation time to prevent overflow
    if (animationTime > 4.0f * M_PI) {
        animationTime = 0.0f;
    }
}

void AnimationSystem::updateKungFuAnimation() {
    animationTime += 0.2f;  // Kung fu animation speed

    // Kung fu fighting sequence
    float phase = fmodf(animationTime, 4.0f * M_PI);  // 4-phase cycle

    if (phase < M_PI) {
        // Phase 1: Ready stance
        application->leftUpperArmX = -45.0f;   // Guard position
        application->rightUpperArmX = -45.0f;
        application->leftUpperArmZ = 30.0f;    // Arms out
        application->rightUpperArmZ = -30.0f;
        application->leftForearmX = -60.0f;    // Bent elbows
        application->rightForearmX = -60.0f;

        // Stable leg stance
        application->leftThighX = -10.0f;
        application->rightThighX = -10.0f;
        application->leftLowerLegX = 20.0f;
        application->rightLowerLegX = 20.0f;

    } else if (phase < 2.0f * M_PI) {
        // Phase 2: Left punch
        application->leftUpperArmX = 0.0f;     // Punch forward
        application->rightUpperArmX = -45.0f;  // Guard
        application->leftUpperArmZ = 0.0f;     // Straight
        application->rightUpperArmZ = -30.0f;
        application->leftForearmX = 0.0f;      // Straight arm
        application->rightForearmX = -60.0f;   // Bent guard

        // Forward stance
        application->leftThighX = 20.0f;       // Forward leg
        application->rightThighX = -30.0f;     // Back leg
        application->leftLowerLegX = 10.0f;
        application->rightLowerLegX = 40.0f;

    } else if (phase < 3.0f * M_PI) {
        // Phase 3: Right punch
        application->leftUpperArmX = -45.0f;   // Guard
        application->rightUpperArmX = 0.0f;    // Punch forward
        application->leftUpperArmZ = 30.0f;
        application->rightUpperArmZ = 0.0f;    // Straight
        application->leftForearmX = -60.0f;    // Bent guard
        application->rightForearmX = 0.0f;     // Straight arm

        // Opposite stance
        application->leftThighX = -30.0f;      // Back leg
        application->rightThighX = 20.0f;      // Forward leg
        application->leftLowerLegX = 40.0f;
        application->rightLowerLegX = 10.0f;

    } else {
        // Phase 4: High kick
        application->leftUpperArmX = -60.0f;   // Balance arms
        application->rightUpperArmX = -60.0f;
        application->leftUpperArmZ = 45.0f;
        application->rightUpperArmZ = -45.0f;
        application->leftForearmX = -30.0f;
        application->rightForearmX = -30.0f;

        // High kick with right leg
        application->leftThighX = -20.0f;      // Support leg
        application->rightThighX = 60.0f;      // High kick
        application->leftLowerLegX = 30.0f;
        application->rightLowerLegX = -20.0f;  // Kick extension
    }
}
