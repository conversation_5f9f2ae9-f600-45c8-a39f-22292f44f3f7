#include "MenuSystem.hpp"
#include "Application.hpp"
#include <OpenGL/gl.h>
#include <algorithm>

MenuSystem::MenuSystem(Application* app) 
    : application(app), currentSettingsPage(SETTINGS_MAIN), 
      menuBackgroundR(0.1f), menuBackgroundG(0.1f), menuBackgroundB(0.2f),
      currentResolutionIndex(0), fontSize(1), isDragging(false) {
    
    // Initialize available resolutions
    availableResolutions.push_back(Resolution(800, 600, "800x600"));
    availableResolutions.push_back(Resolution(1024, 768, "1024x768"));
    availableResolutions.push_back(Resolution(1280, 720, "1280x720"));
    availableResolutions.push_back(Resolution(1920, 1080, "1920x1080"));
}

MenuSystem::~MenuSystem() {
}

void MenuSystem::setup2DRendering() {
    glDisable(GL_DEPTH_TEST);
    
    glMatrixMode(GL_PROJECTION);
    glLoadIdentity();
    glOrtho(0, application->windowWidth, application->windowHeight, 0, -1, 1);
    glMatrixMode(GL_MODELVIEW);
    glLoadIdentity();
    
    glClearColor(menuBackgroundR, menuBackgroundG, menuBackgroundB, 1.0f);
    glClear(GL_COLOR_BUFFER_BIT | GL_DEPTH_BUFFER_BIT);
}

void MenuSystem::render() {
    setup2DRendering();
    
    switch (application->currentState) {
        case Application::MAIN_MENU:
            renderMainMenu();
            break;
        case Application::SETTINGS_MENU:
            renderSettingsMenu();
            break;
        case Application::CREDITS_MENU:
            renderCreditsMenu();
            break;
        case Application::INSTRUCTIONS_MENU:
            renderInstructionsMenu();
            break;
        default:
            break;
    }
}

void MenuSystem::handleMouseInput(int mouseX, int mouseY, bool mousePressed) {
    // Update sliders if dragging
    for (auto& slider : activeSliders) {
        slider.updateValue(mouseX);
    }
    
    // Handle slider dragging
    if (mousePressed) {
        for (auto& slider : activeSliders) {
            if (slider.isClicked(mouseX, mouseY)) {
                slider.dragging = true;
                isDragging = true;
            }
        }
    } else {
        // Stop dragging all sliders
        for (auto& slider : activeSliders) {
            slider.dragging = false;
        }
        isDragging = false;
    }
}

void MenuSystem::renderChar(char c, int x, int y, float r, float g, float b) {
    glColor3f(r, g, b);

    // Simple 5x7 bitmap font patterns for basic characters
    bool pattern[7][5] = {false};

    switch (c) {
        case 'A': case 'a':
            pattern[0][1] = pattern[0][2] = pattern[0][3] = true;
            pattern[1][0] = pattern[1][4] = true;
            pattern[2][0] = pattern[2][4] = true;
            pattern[3][0] = pattern[3][1] = pattern[3][2] = pattern[3][3] = pattern[3][4] = true;
            pattern[4][0] = pattern[4][4] = true;
            pattern[5][0] = pattern[5][4] = true;
            pattern[6][0] = pattern[6][4] = true;
            break;
        case 'B': case 'b':
            pattern[0][0] = pattern[0][1] = pattern[0][2] = pattern[0][3] = true;
            pattern[1][0] = pattern[1][4] = true;
            pattern[2][0] = pattern[2][4] = true;
            pattern[3][0] = pattern[3][1] = pattern[3][2] = pattern[3][3] = true;
            pattern[4][0] = pattern[4][4] = true;
            pattern[5][0] = pattern[5][4] = true;
            pattern[6][0] = pattern[6][1] = pattern[6][2] = pattern[6][3] = true;
            break;
        case 'C': case 'c':
            pattern[0][1] = pattern[0][2] = pattern[0][3] = true;
            pattern[1][0] = pattern[1][4] = true;
            pattern[2][0] = true;
            pattern[3][0] = true;
            pattern[4][0] = true;
            pattern[5][0] = pattern[5][4] = true;
            pattern[6][1] = pattern[6][2] = pattern[6][3] = true;
            break;
        case 'D': case 'd':
            pattern[0][0] = pattern[0][1] = pattern[0][2] = pattern[0][3] = true;
            pattern[1][0] = pattern[1][4] = true;
            pattern[2][0] = pattern[2][4] = true;
            pattern[3][0] = pattern[3][4] = true;
            pattern[4][0] = pattern[4][4] = true;
            pattern[5][0] = pattern[5][4] = true;
            pattern[6][0] = pattern[6][1] = pattern[6][2] = pattern[6][3] = true;
            break;
        case 'E': case 'e':
            pattern[0][0] = pattern[0][1] = pattern[0][2] = pattern[0][3] = pattern[0][4] = true;
            pattern[1][0] = true;
            pattern[2][0] = true;
            pattern[3][0] = pattern[3][1] = pattern[3][2] = pattern[3][3] = true;
            pattern[4][0] = true;
            pattern[5][0] = true;
            pattern[6][0] = pattern[6][1] = pattern[6][2] = pattern[6][3] = pattern[6][4] = true;
            break;
        case 'F': case 'f':
            pattern[0][0] = pattern[0][1] = pattern[0][2] = pattern[0][3] = pattern[0][4] = true;
            pattern[1][0] = true;
            pattern[2][0] = true;
            pattern[3][0] = pattern[3][1] = pattern[3][2] = pattern[3][3] = true;
            pattern[4][0] = true;
            pattern[5][0] = true;
            pattern[6][0] = true;
            break;
        case 'G': case 'g':
            pattern[0][1] = pattern[0][2] = pattern[0][3] = true;
            pattern[1][0] = pattern[1][4] = true;
            pattern[2][0] = true;
            pattern[3][0] = pattern[3][2] = pattern[3][3] = pattern[3][4] = true;
            pattern[4][0] = pattern[4][4] = true;
            pattern[5][0] = pattern[5][4] = true;
            pattern[6][1] = pattern[6][2] = pattern[6][3] = true;
            break;
        case 'H': case 'h':
            pattern[0][0] = pattern[0][4] = true;
            pattern[1][0] = pattern[1][4] = true;
            pattern[2][0] = pattern[2][4] = true;
            pattern[3][0] = pattern[3][1] = pattern[3][2] = pattern[3][3] = pattern[3][4] = true;
            pattern[4][0] = pattern[4][4] = true;
            pattern[5][0] = pattern[5][4] = true;
            pattern[6][0] = pattern[6][4] = true;
            break;
        case 'I': case 'i':
            pattern[0][1] = pattern[0][2] = pattern[0][3] = true;
            pattern[1][2] = true;
            pattern[2][2] = true;
            pattern[3][2] = true;
            pattern[4][2] = true;
            pattern[5][2] = true;
            pattern[6][1] = pattern[6][2] = pattern[6][3] = true;
            break;
        case ' ':
            // Space - no pattern
            break;
        default:
            // Unknown character - draw a small rectangle
            pattern[2][1] = pattern[2][2] = pattern[2][3] = true;
            pattern[3][1] = pattern[3][2] = pattern[3][3] = true;
            pattern[4][1] = pattern[4][2] = pattern[4][3] = true;
            break;
    }

    // Render the pattern
    for (int row = 0; row < 7; row++) {
        for (int col = 0; col < 5; col++) {
            if (pattern[row][col]) {
                glBegin(GL_QUADS);
                glVertex2f(static_cast<float>(x + col), static_cast<float>(y + row));
                glVertex2f(static_cast<float>(x + col + 1), static_cast<float>(y + row));
                glVertex2f(static_cast<float>(x + col + 1), static_cast<float>(y + row + 1));
                glVertex2f(static_cast<float>(x + col), static_cast<float>(y + row + 1));
                glEnd();
            }
        }
    }
}

void MenuSystem::renderText(const std::string& text, int x, int y, float r, float g, float b) {
    int currentX = x;
    for (char c : text) {
        renderChar(c, currentX, y, r, g, b);
        currentX += 6; // Character spacing
    }
}

void MenuSystem::renderButton(const MenuButton& button) {
    // Button background
    if (button.hovered) {
        glColor3f(0.3f, 0.3f, 0.7f); // Highlighted
    } else {
        glColor3f(0.2f, 0.2f, 0.5f); // Normal
    }

    glBegin(GL_QUADS);
    glVertex2f(static_cast<float>(button.x), static_cast<float>(button.y));
    glVertex2f(static_cast<float>(button.x + button.width), static_cast<float>(button.y));
    glVertex2f(static_cast<float>(button.x + button.width), static_cast<float>(button.y + button.height));
    glVertex2f(static_cast<float>(button.x), static_cast<float>(button.y + button.height));
    glEnd();

    // Button border
    glColor3f(1.0f, 1.0f, 1.0f);
    glBegin(GL_LINE_LOOP);
    glVertex2f(static_cast<float>(button.x), static_cast<float>(button.y));
    glVertex2f(static_cast<float>(button.x + button.width), static_cast<float>(button.y));
    glVertex2f(static_cast<float>(button.x + button.width), static_cast<float>(button.y + button.height));
    glVertex2f(static_cast<float>(button.x), static_cast<float>(button.y + button.height));
    glEnd();

    // Button text (properly centered)
    int textWidth = static_cast<int>(button.text.length()) * 6; // 6 pixels per character
    int textX = button.x + (button.width - textWidth) / 2;
    int textY = button.y + button.height / 2 - 3; // Center vertically (7 pixel height / 2)
    renderText(button.text, textX, textY, 1.0f, 1.0f, 1.0f);
}

void MenuSystem::renderMainMenu() {
    std::string title = "HUMANGL - SKELETAL ANIMATION";
    int titleWidth = static_cast<int>(title.length()) * 6; // 6 pixels per character
    renderText(title, application->windowWidth/2 - titleWidth/2, 100, 1.0f, 1.0f, 0.0f);

    // Create menu buttons
    std::vector<MenuButton> buttons = {
        MenuButton(application->windowWidth/2 - 100, 200, 200, 50, "START SIMULATION"),
        MenuButton(application->windowWidth/2 - 100, 270, 200, 50, "SETTINGS"),
        MenuButton(application->windowWidth/2 - 100, 340, 200, 50, "INSTRUCTIONS"),
        MenuButton(application->windowWidth/2 - 100, 410, 200, 50, "CREDITS"),
        MenuButton(application->windowWidth/2 - 100, 480, 200, 50, "EXIT")
    };

    // Update button hover states and render
    for (auto& button : buttons) {
        button.hovered = button.isClicked(application->mouseX, application->mouseY);
        renderButton(button);
    }

    // Handle button clicks
    if (application->mousePressed) {
        if (buttons[0].hovered) application->currentState = Application::SIMULATION;
        else if (buttons[1].hovered) application->currentState = Application::SETTINGS_MENU;
        else if (buttons[2].hovered) application->currentState = Application::INSTRUCTIONS_MENU;
        else if (buttons[3].hovered) application->currentState = Application::CREDITS_MENU;
        else if (buttons[4].hovered) application->running = false;
    }
}

void MenuSystem::renderSettingsMenu() {
    // Placeholder for now - just show a simple message
    std::string title = "SETTINGS";
    int titleWidth = static_cast<int>(title.length()) * 6;
    renderText(title, application->windowWidth/2 - titleWidth/2, 100, 1.0f, 1.0f, 0.0f);

    std::string message = "Settings implementation in progress...";
    int messageWidth = static_cast<int>(message.length()) * 6;
    renderText(message, application->windowWidth/2 - messageWidth/2, 200, 0.8f, 0.8f, 0.8f);

    // Back button
    MenuButton backButton(50, application->windowHeight - 100, 100, 40, "Back");
    backButton.hovered = backButton.isClicked(application->mouseX, application->mouseY);
    renderButton(backButton);

    if (application->mousePressed && backButton.hovered) {
        application->currentState = Application::MAIN_MENU;
    }
}

void MenuSystem::renderCreditsMenu() {
    std::string title = "CREDITS";
    int titleWidth = static_cast<int>(title.length()) * 6;
    renderText(title, application->windowWidth/2 - titleWidth/2, 100, 1.0f, 1.0f, 0.0f);

    std::string line1 = "HumanGL Skeletal Animation System";
    int line1Width = static_cast<int>(line1.length()) * 6;
    renderText(line1, application->windowWidth/2 - line1Width/2, 200, 1.0f, 1.0f, 1.0f);

    std::string line2 = "Developed with C++14 and SDL2";
    int line2Width = static_cast<int>(line2.length()) * 6;
    renderText(line2, application->windowWidth/2 - line2Width/2, 230, 0.8f, 0.8f, 0.8f);

    std::string line3 = "OpenGL 2.1 Compatible";
    int line3Width = static_cast<int>(line3.length()) * 6;
    renderText(line3, application->windowWidth/2 - line3Width/2, 260, 0.8f, 0.8f, 0.8f);

    MenuButton backButton(50, application->windowHeight - 100, 100, 40, "Back");
    backButton.hovered = backButton.isClicked(application->mouseX, application->mouseY);
    renderButton(backButton);

    if (application->mousePressed && backButton.hovered) {
        application->currentState = Application::MAIN_MENU;
    }
}

void MenuSystem::renderInstructionsMenu() {
    std::string title = "INSTRUCTIONS";
    int titleWidth = static_cast<int>(title.length()) * 6;
    renderText(title, application->windowWidth/2 - titleWidth/2, 80, 1.0f, 1.0f, 0.0f);

    int y = 130;
    renderText("ANIMATIONS:", 50, y, 1.0f, 1.0f, 0.0f); y += 30;
    renderText("  SPACE - Toggle walking animation", 50, y, 1.0f, 1.0f, 1.0f); y += 25;
    renderText("  P - Jump (single jump)", 50, y, 1.0f, 1.0f, 1.0f); y += 25;
    renderText("  J - Toggle disco dancing", 50, y, 1.0f, 1.0f, 1.0f); y += 25;
    renderText("  K - Toggle kung fu fighting", 50, y, 1.0f, 1.0f, 1.0f); y += 35;

    renderText("CONTROLS:", 50, y, 1.0f, 1.0f, 0.0f); y += 30;
    renderText("  A/D - Rotate torso (all parts follow)", 50, y, 1.0f, 1.0f, 1.0f); y += 25;
    renderText("  W/S - Head up/down, Q/E - Head left/right", 50, y, 1.0f, 1.0f, 1.0f); y += 25;
    renderText("  1-6 - Manual arm controls", 50, y, 1.0f, 1.0f, 1.0f); y += 25;
    renderText("  7-0 - Manual leg controls", 50, y, 1.0f, 1.0f, 1.0f); y += 25;
    renderText("  M - Return to main menu", 50, y, 1.0f, 1.0f, 1.0f); y += 25;
    renderText("  ESC - Exit application", 50, y, 1.0f, 1.0f, 1.0f);

    MenuButton backButton(50, application->windowHeight - 100, 100, 40, "Back");
    backButton.hovered = backButton.isClicked(application->mouseX, application->mouseY);
    renderButton(backButton);

    if (application->mousePressed && backButton.hovered) {
        application->currentState = Application::MAIN_MENU;
    }
}
