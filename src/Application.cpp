#include "Application.hpp"

Application::Application() 
    : window(nullptr), glContext(nullptr), running(false),
      windowWidth(800), windowHeight(600), 
      mouseX(0), mouseY(0), mousePressed(false),
      currentState(MAIN_MENU),
      torsoRotationY(0.0f), headRotationX(0.0f), headRotationY(0.0f),
      leftUpperArmX(0.0f), leftUpperArmZ(0.0f), leftForearmX(0.0f),
      rightUpperArmX(0.0f), rightUpperArmZ(0.0f), rightForearmX(0.0f),
      leftThighX(0.0f), leftLowerLegX(0.0f),
      rightThighX(0.0f), rightLowerLegX(0.0f),
      keyboardState(nullptr) {
    
    // Initialize body part settings with different default colors
    headSettings = BodyPartSettings(0.8f, 0.6f, 0.4f);  // Skin tone
    torsoSettings = BodyPartSettings(0.2f, 0.4f, 0.8f); // Blue shirt
    leftArmSettings = BodyPartSettings(0.8f, 0.6f, 0.4f); // Skin tone
    rightArmSettings = BodyPartSettings(0.8f, 0.6f, 0.4f); // Skin tone
    leftLegSettings = BodyPartSettings(0.1f, 0.1f, 0.5f); // Dark blue pants
    rightLegSettings = BodyPartSettings(0.1f, 0.1f, 0.5f); // Dark blue pants
    
    menuSystem = nullptr;
    animationSystem = nullptr;
}

Application::~Application() {
    cleanup();
}

bool Application::init() {
    if (!initSDL()) {
        return false;
    }
    
    if (!initOpenGL()) {
        return false;
    }
    
    // Initialize systems
    menuSystem = new MenuSystem(this);
    animationSystem = new AnimationSystem(this);
    
    // Get keyboard state
    keyboardState = SDL_GetKeyboardState(nullptr);
    
    running = true;
    
    std::cout << "=== HumanGL Skeletal Animation System ===" << std::endl;
    std::cout << "Menu-driven application with full skeletal animation!" << std::endl;
    std::cout << "Use mouse to navigate menus." << std::endl;
    std::cout << "Press ESC to exit at any time." << std::endl;
    
    return true;
}

bool Application::initSDL() {
    if (SDL_Init(SDL_INIT_VIDEO) < 0) {
        std::cerr << "SDL could not initialize! SDL Error: " << SDL_GetError() << std::endl;
        return false;
    }

    // Set OpenGL attributes
    SDL_GL_SetAttribute(SDL_GL_CONTEXT_MAJOR_VERSION, 2);
    SDL_GL_SetAttribute(SDL_GL_CONTEXT_MINOR_VERSION, 1);
    SDL_GL_SetAttribute(SDL_GL_DOUBLEBUFFER, 1);
    SDL_GL_SetAttribute(SDL_GL_DEPTH_SIZE, 24);

    // Create window
    window = SDL_CreateWindow(
        "HumanGL - Skeletal Animation System",
        SDL_WINDOWPOS_CENTERED,
        SDL_WINDOWPOS_CENTERED,
        windowWidth,
        windowHeight,
        SDL_WINDOW_OPENGL | SDL_WINDOW_SHOWN
    );

    if (window == nullptr) {
        std::cerr << "Window could not be created! SDL Error: " << SDL_GetError() << std::endl;
        return false;
    }

    // Create OpenGL context
    glContext = SDL_GL_CreateContext(window);
    if (glContext == nullptr) {
        std::cerr << "OpenGL context could not be created! SDL Error: " << SDL_GetError() << std::endl;
        return false;
    }

    // Enable VSync
    if (SDL_GL_SetSwapInterval(1) < 0) {
        std::cerr << "Warning: Unable to set VSync! SDL Error: " << SDL_GetError() << std::endl;
    }

    return true;
}

bool Application::initOpenGL() {
    // Enable depth testing
    glEnable(GL_DEPTH_TEST);
    glDepthFunc(GL_LESS);

    // Set up the viewport
    glViewport(0, 0, windowWidth, windowHeight);

    setupPerspective();

    // Set clear color
    glClearColor(0.2f, 0.3f, 0.4f, 1.0f);

    return true;
}

void Application::setupPerspective() {
    glMatrixMode(GL_PROJECTION);
    glLoadIdentity();
    
    GLdouble fovy = 45.0;
    GLdouble aspect = static_cast<GLdouble>(windowWidth) / static_cast<GLdouble>(windowHeight);
    GLdouble nearPlane = 0.1;
    GLdouble farPlane = 100.0;
    
    gluPerspective(fovy, aspect, nearPlane, farPlane);
    
    glMatrixMode(GL_MODELVIEW);
    glLoadIdentity();
}

void Application::cleanup() {
    delete menuSystem;
    delete animationSystem;
    
    if (glContext) {
        SDL_GL_DeleteContext(glContext);
        glContext = nullptr;
    }

    if (window) {
        SDL_DestroyWindow(window);
        window = nullptr;
    }

    SDL_Quit();
}

void Application::run() {
    while (running) {
        handleEvents();
        
        // Only update animations and handle input in simulation mode
        if (currentState == SIMULATION) {
            animationSystem->update();
            handleKeyboardInput();
        }
        
        render();
        
        // Small delay to prevent excessive CPU usage
        SDL_Delay(16); // ~60 FPS
    }
}

void Application::handleEvents() {
    SDL_Event event;
    while (SDL_PollEvent(&event)) {
        switch (event.type) {
            case SDL_QUIT:
                running = false;
                break;
            case SDL_MOUSEMOTION:
                mouseX = event.motion.x;
                mouseY = event.motion.y;
                break;
            case SDL_MOUSEBUTTONDOWN:
                if (event.button.button == SDL_BUTTON_LEFT) {
                    mousePressed = true;
                }
                break;
            case SDL_MOUSEBUTTONUP:
                if (event.button.button == SDL_BUTTON_LEFT) {
                    mousePressed = false;
                }
                break;
            case SDL_KEYDOWN:
                if (event.key.keysym.sym == SDLK_ESCAPE) {
                    running = false;
                }
                if (event.key.keysym.sym == SDLK_m && currentState == SIMULATION) {
                    currentState = MAIN_MENU;
                    // Reset animation when returning to menu
                    animationSystem->resetAnimation();
                    // Reset limb positions
                    leftThighX = rightThighX = 0.0f;
                    leftLowerLegX = rightLowerLegX = 0.0f;
                    leftUpperArmX = rightUpperArmX = 0.0f;
                    leftUpperArmZ = rightUpperArmZ = 0.0f;
                    leftForearmX = rightForearmX = 0.0f;
                    headRotationX = headRotationY = 0.0f;
                    torsoRotationY = 0.0f;
                }

                // Animation controls only work in simulation mode
                if (currentState == SIMULATION) {
                    if (event.key.keysym.sym == SDLK_SPACE) {
                        // Toggle walking mode
                        if (animationSystem->getCurrentAnimation() == AnimationSystem::WALKING) {
                            animationSystem->resetAnimation();
                            // Reset limb positions
                            leftThighX = rightThighX = 0.0f;
                            leftLowerLegX = rightLowerLegX = 0.0f;
                            leftUpperArmX = rightUpperArmX = 0.0f;
                            leftUpperArmZ = rightUpperArmZ = 0.0f;
                            leftForearmX = rightForearmX = 0.0f;
                        } else if (animationSystem->getCurrentAnimation() == AnimationSystem::NONE) {
                            animationSystem->setAnimation(AnimationSystem::WALKING);
                        }
                    }
                    if (event.key.keysym.sym == SDLK_p) {
                        if (animationSystem->getCurrentAnimation() == AnimationSystem::NONE) {
                            animationSystem->setAnimation(AnimationSystem::JUMPING);
                        }
                    }
                    if (event.key.keysym.sym == SDLK_j) {  // J for dancing
                        if (animationSystem->getCurrentAnimation() == AnimationSystem::DANCING) {
                            animationSystem->resetAnimation();
                            // Reset limb positions
                            leftThighX = rightThighX = 0.0f;
                            leftLowerLegX = rightLowerLegX = 0.0f;
                            leftUpperArmX = rightUpperArmX = 0.0f;
                            leftUpperArmZ = rightUpperArmZ = 0.0f;
                            leftForearmX = rightForearmX = 0.0f;
                            headRotationX = headRotationY = 0.0f;
                        } else if (animationSystem->getCurrentAnimation() == AnimationSystem::NONE) {
                            animationSystem->setAnimation(AnimationSystem::DANCING);
                        }
                    }
                    if (event.key.keysym.sym == SDLK_k) {  // K for kung fu
                        if (animationSystem->getCurrentAnimation() == AnimationSystem::KUNGFU) {
                            animationSystem->resetAnimation();
                            // Reset limb positions
                            leftThighX = rightThighX = 0.0f;
                            leftLowerLegX = rightLowerLegX = 0.0f;
                            leftUpperArmX = rightUpperArmX = 0.0f;
                            leftUpperArmZ = rightUpperArmZ = 0.0f;
                            leftForearmX = rightForearmX = 0.0f;
                        } else if (animationSystem->getCurrentAnimation() == AnimationSystem::NONE) {
                            animationSystem->setAnimation(AnimationSystem::KUNGFU);
                        }
                    }
                } // End simulation mode check
                break;
            default:
                break;
        }
    }

    // Pass mouse input to menu system
    if (currentState != SIMULATION) {
        menuSystem->handleMouseInput(mouseX, mouseY, mousePressed);
    }
}

void Application::handleKeyboardInput() {
    // Manual limb controls (only when no animation is playing)
    if (animationSystem->getCurrentAnimation() == AnimationSystem::NONE) {
        // Torso rotation (always available)
        if (keyboardState[SDL_SCANCODE_A]) {
            torsoRotationY -= 2.0f;
        }
        if (keyboardState[SDL_SCANCODE_D]) {
            torsoRotationY += 2.0f;
        }

        // Head rotation
        if (keyboardState[SDL_SCANCODE_W]) {
            headRotationX -= 2.0f;
            if (headRotationX < -45.0f) headRotationX = -45.0f;
        }
        if (keyboardState[SDL_SCANCODE_S]) {
            headRotationX += 2.0f;
            if (headRotationX > 45.0f) headRotationX = 45.0f;
        }
        if (keyboardState[SDL_SCANCODE_Q]) {
            headRotationY -= 2.0f;
            if (headRotationY < -60.0f) headRotationY = -60.0f;
        }
        if (keyboardState[SDL_SCANCODE_E]) {
            headRotationY += 2.0f;
            if (headRotationY > 60.0f) headRotationY = 60.0f;
        }

        // Left arm controls
        if (keyboardState[SDL_SCANCODE_1]) {
            leftUpperArmX -= 2.0f;
            if (leftUpperArmX < -90.0f) leftUpperArmX = -90.0f;
        }
        if (keyboardState[SDL_SCANCODE_2]) {
            leftUpperArmX += 2.0f;
            if (leftUpperArmX > 90.0f) leftUpperArmX = 90.0f;
        }
        if (keyboardState[SDL_SCANCODE_3]) {
            leftForearmX -= 2.0f;
            if (leftForearmX < -120.0f) leftForearmX = -120.0f;
        }

        // Right arm controls
        if (keyboardState[SDL_SCANCODE_4]) {
            rightUpperArmX -= 2.0f;
            if (rightUpperArmX < -90.0f) rightUpperArmX = -90.0f;
        }
        if (keyboardState[SDL_SCANCODE_5]) {
            rightUpperArmX += 2.0f;
            if (rightUpperArmX > 90.0f) rightUpperArmX = 90.0f;
        }
        if (keyboardState[SDL_SCANCODE_6]) {
            rightForearmX -= 2.0f;
            if (rightForearmX < -120.0f) rightForearmX = -120.0f;
        }

        // Left leg controls
        if (keyboardState[SDL_SCANCODE_7]) {
            leftThighX -= 2.0f;
            if (leftThighX < -45.0f) leftThighX = -45.0f;
        }
        if (keyboardState[SDL_SCANCODE_8]) {
            leftThighX += 2.0f;
            if (leftThighX > 45.0f) leftThighX = 45.0f;
        }

        // Right leg controls
        if (keyboardState[SDL_SCANCODE_9]) {
            rightThighX -= 2.0f;
            if (rightThighX < -45.0f) rightThighX = -45.0f;
        }
        if (keyboardState[SDL_SCANCODE_0]) {
            rightThighX += 2.0f;
            if (rightThighX > 45.0f) rightThighX = 45.0f;
        }
    }
}

void Application::render() {
    switch (currentState) {
        case MAIN_MENU:
        case SETTINGS_MENU:
        case CREDITS_MENU:
        case INSTRUCTIONS_MENU:
            menuSystem->render();
            break;
        case SIMULATION:
            renderSimulation();
            break;
    }

    // Swap buffers
    SDL_GL_SwapWindow(window);
}

void Application::renderSimulation() {
    // Enable depth testing for 3D rendering
    glEnable(GL_DEPTH_TEST);

    // Set up 3D rendering
    glMatrixMode(GL_PROJECTION);
    glLoadIdentity();
    GLdouble fovy = 45.0;
    GLdouble aspect = static_cast<GLdouble>(windowWidth) / static_cast<GLdouble>(windowHeight);
    GLdouble nearPlane = 0.1;
    GLdouble farPlane = 100.0;
    gluPerspective(fovy, aspect, nearPlane, farPlane);
    glMatrixMode(GL_MODELVIEW);
    glLoadIdentity();

    // Clear the screen with simulation background
    glClearColor(0.2f, 0.3f, 0.4f, 1.0f);
    glClear(GL_COLOR_BUFFER_BIT | GL_DEPTH_BUFFER_BIT);

    // Reset matrix stack
    matrixStack.loadIdentity();

    // Apply jump height (affects entire body)
    matrixStack.translate(0.0f, animationSystem->getJumpHeight(), 0.0f);

    // Apply torso rotation (this affects all body parts)
    matrixStack.rotateY(torsoRotationY);

    // Draw body parts in hierarchical order
    drawTorso();           // Base of hierarchy
    drawNeck();            // Connected to torso
    drawHead();            // Connected to neck
    drawEyes();            // Eyes on the head (shows front direction)

    // Draw shoulder joints
    drawLeftShoulder();    // Left shoulder joint
    drawRightShoulder();   // Right shoulder joint

    // Draw arms (connected to shoulders)
    drawLeftUpperArm();    // Left upper arm
    drawLeftForearm();     // Left forearm (follows upper arm)
    drawRightUpperArm();   // Right upper arm
    drawRightForearm();    // Right forearm (follows upper arm)

    // Draw legs (connected to torso)
    drawLeftThigh();       // Left thigh
    drawLeftLowerLeg();    // Left lower leg (follows thigh)
    drawRightThigh();      // Right thigh
    drawRightLowerLeg();   // Right lower leg (follows thigh)
}

void Application::drawColoredCube(float r, float g, float b) {
    glColor3f(r, g, b);
    drawUnitCube();
}

void Application::drawUnitCube() {
    glBegin(GL_QUADS);

    // Front face
    glVertex3f(-0.5f, -0.5f,  0.5f);
    glVertex3f( 0.5f, -0.5f,  0.5f);
    glVertex3f( 0.5f,  0.5f,  0.5f);
    glVertex3f(-0.5f,  0.5f,  0.5f);

    // Back face
    glVertex3f(-0.5f, -0.5f, -0.5f);
    glVertex3f(-0.5f,  0.5f, -0.5f);
    glVertex3f( 0.5f,  0.5f, -0.5f);
    glVertex3f( 0.5f, -0.5f, -0.5f);

    // Top face
    glVertex3f(-0.5f,  0.5f, -0.5f);
    glVertex3f(-0.5f,  0.5f,  0.5f);
    glVertex3f( 0.5f,  0.5f,  0.5f);
    glVertex3f( 0.5f,  0.5f, -0.5f);

    // Bottom face
    glVertex3f(-0.5f, -0.5f, -0.5f);
    glVertex3f( 0.5f, -0.5f, -0.5f);
    glVertex3f( 0.5f, -0.5f,  0.5f);
    glVertex3f(-0.5f, -0.5f,  0.5f);

    // Right face
    glVertex3f( 0.5f, -0.5f, -0.5f);
    glVertex3f( 0.5f,  0.5f, -0.5f);
    glVertex3f( 0.5f,  0.5f,  0.5f);
    glVertex3f( 0.5f, -0.5f,  0.5f);

    // Left face
    glVertex3f(-0.5f, -0.5f, -0.5f);
    glVertex3f(-0.5f, -0.5f,  0.5f);
    glVertex3f(-0.5f,  0.5f,  0.5f);
    glVertex3f(-0.5f,  0.5f, -0.5f);

    glEnd();
}
