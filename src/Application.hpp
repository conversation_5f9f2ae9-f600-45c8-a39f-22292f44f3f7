#pragma once

#include <SDL2/SDL.h>
#include <OpenGL/gl.h>
#include <OpenGL/glu.h>
#include <iostream>
#include <vector>
#include <string>

#include "MatrixStack.hpp"
#include "MenuSystem.hpp"
#include "AnimationSystem.hpp"
#include "BodyPartSettings.hpp"

class Application {
public:
    Application();
    ~Application();
    
    bool init();
    void run();
    void cleanup();

private:
    // SDL and OpenGL
    SDL_Window* window;
    SDL_GLContext glContext;
    bool running;
    int windowWidth;
    int windowHeight;
    
    // Input
    int mouseX, mouseY;
    bool mousePressed;
    const Uint8* keyboardState;
    
    // Core systems
    MatrixStack matrixStack;
    MenuSystem* menuSystem;
    AnimationSystem* animationSystem;
    
    // Body part settings
    BodyPartSettings headSettings;
    BodyPartSettings torsoSettings;
    BodyPartSettings leftArmSettings;
    BodyPartSettings rightArmSettings;
    BodyPartSettings leftLegSettings;
    BodyPartSettings rightLegSettings;
    
    // Application state
    enum AppState {
        MAIN_MENU,
        SETTINGS_MENU,
        CREDITS_MENU,
        INSTRUCTIONS_MENU,
        SIMULATION
    };
    
    AppState currentState;
    
    // Body part rotations (for manual control)
    float torsoRotationY;
    float headRotationX, headRotationY;
    float leftUpperArmX, leftUpperArmZ, leftForearmX;
    float rightUpperArmX, rightUpperArmZ, rightForearmX;
    float leftThighX, leftLowerLegX;
    float rightThighX, rightLowerLegX;
    
    // Methods
    bool initSDL();
    bool initOpenGL();
    void setupPerspective();
    void handleEvents();
    void handleKeyboardInput();
    void render();
    void renderSimulation();
    
    // Body part drawing
    void drawTorso();
    void drawNeck();
    void drawHead();
    void drawEyes();
    void drawLeftShoulder();
    void drawRightShoulder();
    void drawLeftUpperArm();
    void drawLeftForearm();
    void drawRightUpperArm();
    void drawRightForearm();
    void drawLeftThigh();
    void drawLeftLowerLeg();
    void drawRightThigh();
    void drawRightLowerLeg();
    
    // Utility drawing methods
    void drawColoredCube(float r, float g, float b);
    void drawUnitCube();
    
    // Getters for other systems
    friend class MenuSystem;
    friend class AnimationSystem;
};
