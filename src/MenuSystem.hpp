#pragma once

#include <vector>
#include <string>
#include "BodyPartSettings.hpp"

class Application; // Forward declaration

class MenuSystem {
public:
    MenuSystem(Application* app);
    ~MenuSystem();
    
    void render();
    void handleMouseInput(int mouseX, int mouseY, bool mousePressed);
    
    // Menu states
    enum SettingsPage {
        SETTINGS_MAIN,
        BODY_CUSTOMIZATION,
        RESOLUTION_SETTINGS
    };
    
    SettingsPage getCurrentSettingsPage() const { return currentSettingsPage; }
    void setCurrentSettingsPage(SettingsPage page) { currentSettingsPage = page; }

private:
    Application* application;
    
    // Settings state
    SettingsPage currentSettingsPage;
    
    // Menu colors
    float menuBackgroundR, menuBackgroundG, menuBackgroundB;
    
    // Resolution settings
    struct Resolution {
        int width, height;
        std::string name;
        Resolution(int w, int h, const std::string& n) : width(w), height(h), name(n) {}
    };
    
    std::vector<Resolution> availableResolutions;
    int currentResolutionIndex;
    int fontSize; // Multiplier for text size (1-3)
    
    // UI Components
    struct MenuButton {
        int x, y, width, height;
        std::string text;
        bool hovered;
        
        MenuButton(int x, int y, int w, int h, const std::string& t) 
            : x(x), y(y), width(w), height(h), text(t), hovered(false) {}
        
        bool isClicked(int mouseX, int mouseY) const {
            return mouseX >= x && mouseX <= x + width && 
                   mouseY >= y && mouseY <= y + height;
        }
    };
    
    struct Slider {
        int x, y, width, height;
        float minValue, maxValue, currentValue;
        std::string label;
        bool dragging;
        
        Slider(int x, int y, int w, int h, float min, float max, float current, const std::string& lbl)
            : x(x), y(y), width(w), height(h), minValue(min), maxValue(max), 
              currentValue(current), label(lbl), dragging(false) {}
        
        bool isClicked(int mouseX, int mouseY) const {
            return mouseX >= x && mouseX <= x + width && 
                   mouseY >= y && mouseY <= y + height;
        }
        
        void updateValue(int mouseX) {
            if (dragging) {
                float ratio = static_cast<float>(mouseX - x) / static_cast<float>(width);
                ratio = std::max(0.0f, std::min(1.0f, ratio));
                currentValue = minValue + ratio * (maxValue - minValue);
            }
        }
    };
    
    struct ColorPicker {
        int x, y, size;
        float* colorR;
        float* colorG;
        float* colorB;
        std::string label;
        
        ColorPicker(int x, int y, int s, float* r, float* g, float* b, const std::string& lbl)
            : x(x), y(y), size(s), colorR(r), colorG(g), colorB(b), label(lbl) {}
        
        bool isClicked(int mouseX, int mouseY) const {
            return mouseX >= x && mouseX <= x + size && 
                   mouseY >= y && mouseY <= y + size;
        }
    };
    
    // Active UI elements
    std::vector<Slider> activeSliders;
    std::vector<ColorPicker> activeColorPickers;
    bool isDragging;
    
    // Rendering methods
    void renderMainMenu();
    void renderSettingsMenu();
    void renderMainSettingsPage();
    void renderBodyCustomizationPage();
    void renderResolutionSettingsPage();
    void renderCreditsMenu();
    void renderInstructionsMenu();
    
    // UI rendering helpers
    void renderText(const std::string& text, int x, int y, float r = 1.0f, float g = 1.0f, float b = 1.0f);
    void renderChar(char c, int x, int y, float r, float g, float b);
    void renderButton(const MenuButton& button);
    void renderSlider(Slider& slider);
    void renderColorPicker(const ColorPicker& picker);
    
    // Utility methods
    void setup2DRendering();
};
