#pragma once

class Application; // Forward declaration

class AnimationSystem {
public:
    AnimationSystem(Application* app);
    ~AnimationSystem();
    
    // Animation modes
    enum AnimationMode {
        NONE,
        WALKING,
        JUMPING,
        DANCING,
        KUNGFU
    };
    
    void update();
    void setAnimation(AnimationMode mode);
    AnimationMode getCurrentAnimation() const { return currentAnimation; }
    
    void resetAnimation();
    float getJumpHeight() const { return jumpHeight; }

private:
    Application* application;
    
    AnimationMode currentAnimation;
    float animationTime;
    float jumpHeight;
    
    // Animation update methods
    void updateWalkingAnimation();
    void updateJumpingAnimation();
    void updateDancingAnimation();
    void updateKungFuAnimation();
};
