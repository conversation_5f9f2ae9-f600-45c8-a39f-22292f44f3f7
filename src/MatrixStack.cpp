#include "MatrixStack.hpp"

MatrixStack::MatrixStack() {
    loadIdentity();
}

float MatrixStack::degreesToRadians(float degrees) {
    return degrees * M_PI / 180.0f;
}

void MatrixStack::loadIdentity() {
    currentMatrix = Matrix4x4();
}

void MatrixStack::pushMatrix() {
    matrixStack.push(currentMatrix);
}

void MatrixStack::popMatrix() {
    if (!matrixStack.empty()) {
        currentMatrix = matrixStack.top();
        matrixStack.pop();
    }
}

void MatrixStack::translate(float x, float y, float z) {
    Matrix4x4 translateMatrix;
    translateMatrix.m[12] = x;
    translateMatrix.m[13] = y;
    translateMatrix.m[14] = z;
    
    currentMatrix = currentMatrix * translateMatrix;
}

void MatrixStack::rotateX(float degrees) {
    float radians = degreesToRadians(degrees);
    float cosTheta = cosf(radians);
    float sinTheta = sinf(radians);
    
    Matrix4x4 rotateMatrix;
    rotateMatrix.m[5] = cosTheta;
    rotateMatrix.m[6] = -sinTheta;
    rotateMatrix.m[9] = sinTheta;
    rotateMatrix.m[10] = cosTheta;
    
    currentMatrix = currentMatrix * rotateMatrix;
}

void MatrixStack::rotateY(float degrees) {
    float radians = degreesToRadians(degrees);
    float cosTheta = cosf(radians);
    float sinTheta = sinf(radians);
    
    Matrix4x4 rotateMatrix;
    rotateMatrix.m[0] = cosTheta;
    rotateMatrix.m[2] = sinTheta;
    rotateMatrix.m[8] = -sinTheta;
    rotateMatrix.m[10] = cosTheta;
    
    currentMatrix = currentMatrix * rotateMatrix;
}

void MatrixStack::rotateZ(float degrees) {
    float radians = degreesToRadians(degrees);
    float cosTheta = cosf(radians);
    float sinTheta = sinf(radians);
    
    Matrix4x4 rotateMatrix;
    rotateMatrix.m[0] = cosTheta;
    rotateMatrix.m[1] = -sinTheta;
    rotateMatrix.m[4] = sinTheta;
    rotateMatrix.m[5] = cosTheta;
    
    currentMatrix = currentMatrix * rotateMatrix;
}

void MatrixStack::scale(float x, float y, float z) {
    Matrix4x4 scaleMatrix;
    scaleMatrix.m[0] = x;
    scaleMatrix.m[5] = y;
    scaleMatrix.m[10] = z;
    
    currentMatrix = currentMatrix * scaleMatrix;
}

void MatrixStack::applyToOpenGL() {
    glMultMatrixf(currentMatrix.m);
}
