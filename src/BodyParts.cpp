#include "Application.hpp"

void Application::drawTorso() {
    matrixStack.pushMatrix();
    
    // Apply torso customization (scale and base size)
    matrixStack.scale(1.2f * torsoSettings.scaleX, 1.8f * torsoSettings.scaleY, 0.8f * torsoSettings.scaleZ);

    // Apply transformations and draw
    glLoadIdentity();
    glTranslatef(0.0f, 0.0f, -8.0f);  // Move back to see the model
    matrixStack.applyToOpenGL();

    drawColoredCube(torsoSettings.colorR, torsoSettings.colorG, torsoSettings.colorB);  // Customizable torso color

    matrixStack.popMatrix();
}

void Application::drawNeck() {
    matrixStack.pushMatrix();
    
    // Position neck at top of torso
    matrixStack.translate(0.0f, 1.0f, 0.0f);
    
    // Scale to make it look like a neck (thin and short)
    matrixStack.scale(0.3f, 0.4f, 0.3f);

    // Apply transformations and draw
    glLoadIdentity();
    glTranslatef(0.0f, 0.0f, -8.0f);  // Move back to see the model
    matrixStack.applyToOpenGL();

    drawColoredCube(headSettings.colorR, headSettings.colorG, headSettings.colorB);  // Same color as head

    matrixStack.popMatrix();
}

void Application::drawHead() {
    matrixStack.pushMatrix();
    
    // Position head at top of neck
    matrixStack.translate(0.0f, 1.4f, 0.0f);
    
    // Apply head rotation
    matrixStack.rotateX(headRotationX);
    matrixStack.rotateY(headRotationY);
    
    // Apply head customization (scale and base size)
    matrixStack.scale(0.9f * headSettings.scaleX, 0.9f * headSettings.scaleY, 0.9f * headSettings.scaleZ);

    // Apply transformations and draw
    glLoadIdentity();
    glTranslatef(0.0f, 0.0f, -8.0f);  // Move back to see the model
    matrixStack.applyToOpenGL();

    drawColoredCube(headSettings.colorR, headSettings.colorG, headSettings.colorB);  // Customizable head color

    matrixStack.popMatrix();
}

void Application::drawEyes() {
    matrixStack.pushMatrix();
    
    // Position eyes on the front of the head
    matrixStack.translate(0.0f, 1.4f, 0.0f);
    
    // Apply head rotation (eyes follow head)
    matrixStack.rotateX(headRotationX);
    matrixStack.rotateY(headRotationY);
    
    // Move to front of head
    matrixStack.translate(0.0f, 0.1f, 0.5f);

    // Apply transformations
    glLoadIdentity();
    glTranslatef(0.0f, 0.0f, -8.0f);
    matrixStack.applyToOpenGL();

    // Draw two small white cubes for eyes
    glColor3f(1.0f, 1.0f, 1.0f);  // White eyes
    
    // Left eye
    glPushMatrix();
    glTranslatef(-0.2f, 0.0f, 0.0f);
    glScalef(0.1f, 0.1f, 0.1f);
    drawUnitCube();
    glPopMatrix();
    
    // Right eye
    glPushMatrix();
    glTranslatef(0.2f, 0.0f, 0.0f);
    glScalef(0.1f, 0.1f, 0.1f);
    drawUnitCube();
    glPopMatrix();

    matrixStack.popMatrix();
}

void Application::drawLeftShoulder() {
    matrixStack.pushMatrix();
    
    // Position shoulder at top left of torso
    matrixStack.translate(-0.7f, 0.7f, 0.0f);
    
    // Scale to make it look like a shoulder joint (small sphere-like)
    matrixStack.scale(0.3f, 0.3f, 0.3f);

    // Apply transformations and draw
    glLoadIdentity();
    glTranslatef(0.0f, 0.0f, -8.0f);
    matrixStack.applyToOpenGL();

    drawColoredCube(leftArmSettings.colorR, leftArmSettings.colorG, leftArmSettings.colorB);  
	// Same color as left arm

    matrixStack.popMatrix();
}

void Application::drawRightShoulder() {
    matrixStack.pushMatrix();
    
    // Position shoulder at top right of torso
    matrixStack.translate(0.7f, 0.7f, 0.0f);
    
    // Scale to make it look like a shoulder joint (small sphere-like)
    matrixStack.scale(0.3f, 0.3f, 0.3f);

    // Apply transformations and draw
    glLoadIdentity();
    glTranslatef(0.0f, 0.0f, -8.0f);
    matrixStack.applyToOpenGL();

    drawColoredCube(rightArmSettings.colorR, rightArmSettings.colorG, rightArmSettings.colorB);  
	// Same color as right arm

    matrixStack.popMatrix();
}

void Application::drawLeftUpperArm() {
    matrixStack.pushMatrix();
    
    // Position at left shoulder
    matrixStack.translate(-0.7f, 0.7f, 0.0f);
    
    // Apply upper arm rotation
    matrixStack.rotateX(leftUpperArmX);
    matrixStack.rotateZ(leftUpperArmZ);
    
    // Move to center of upper arm
    matrixStack.translate(0.0f, -0.6f, 0.0f);
    
    // Apply arm customization
    matrixStack.scale(0.3f * leftArmSettings.scaleX, 1.2f * leftArmSettings.scaleY, 0.3f * leftArmSettings.scaleZ);

    // Apply transformations and draw
    glLoadIdentity();
    glTranslatef(0.0f, 0.0f, -8.0f);
    matrixStack.applyToOpenGL();

    drawColoredCube(leftArmSettings.colorR, leftArmSettings.colorG, leftArmSettings.colorB);

    matrixStack.popMatrix();
}

void Application::drawLeftForearm() {
    matrixStack.pushMatrix();

    // Position at left shoulder
    matrixStack.translate(-0.7f, 0.7f, 0.0f);

    // Apply upper arm rotation
    matrixStack.rotateX(leftUpperArmX);
    matrixStack.rotateZ(leftUpperArmZ);

    // Move to end of upper arm (elbow position)
    matrixStack.translate(0.0f, -1.2f, 0.0f);

    // Apply forearm rotation
    matrixStack.rotateX(leftForearmX);

    // Move to center of forearm
    matrixStack.translate(0.0f, -0.5f, 0.0f);

    // Scale to make it look like a forearm
    matrixStack.scale(0.25f * leftArmSettings.scaleX, 1.0f * leftArmSettings.scaleY, 0.25f * leftArmSettings.scaleZ);

    // Apply transformations and draw
    glLoadIdentity();
    glTranslatef(0.0f, 0.0f, -8.0f);
    matrixStack.applyToOpenGL();

    drawColoredCube(leftArmSettings.colorR, leftArmSettings.colorG, leftArmSettings.colorB);

    matrixStack.popMatrix();
}

void Application::drawRightUpperArm() {
    matrixStack.pushMatrix();

    // Position at right shoulder
    matrixStack.translate(0.7f, 0.7f, 0.0f);

    // Apply upper arm rotation
    matrixStack.rotateX(rightUpperArmX);
    matrixStack.rotateZ(rightUpperArmZ);

    // Move to center of upper arm
    matrixStack.translate(0.0f, -0.6f, 0.0f);

    // Apply arm customization
    matrixStack.scale(0.3f * rightArmSettings.scaleX, 1.2f * rightArmSettings.scaleY, 0.3f * rightArmSettings.scaleZ);

    // Apply transformations and draw
    glLoadIdentity();
    glTranslatef(0.0f, 0.0f, -8.0f);
    matrixStack.applyToOpenGL();

    drawColoredCube(rightArmSettings.colorR, rightArmSettings.colorG, rightArmSettings.colorB);

    matrixStack.popMatrix();
}

void Application::drawRightForearm() {
    matrixStack.pushMatrix();

    // Position at right shoulder
    matrixStack.translate(0.7f, 0.7f, 0.0f);

    // Apply upper arm rotation
    matrixStack.rotateX(rightUpperArmX);
    matrixStack.rotateZ(rightUpperArmZ);

    // Move to end of upper arm (elbow position)
    matrixStack.translate(0.0f, -1.2f, 0.0f);

    // Apply forearm rotation
    matrixStack.rotateX(rightForearmX);

    // Move to center of forearm
    matrixStack.translate(0.0f, -0.5f, 0.0f);

    // Scale to make it look like a forearm
    matrixStack.scale(0.25f * rightArmSettings.scaleX, 1.0f * rightArmSettings.scaleY, 0.25f * rightArmSettings.scaleZ);

    // Apply transformations and draw
    glLoadIdentity();
    glTranslatef(0.0f, 0.0f, -8.0f);
    matrixStack.applyToOpenGL();

    drawColoredCube(rightArmSettings.colorR, rightArmSettings.colorG, rightArmSettings.colorB);

    matrixStack.popMatrix();
}

void Application::drawLeftThigh() {
    matrixStack.pushMatrix();

    // Position at bottom left of torso
    matrixStack.translate(-0.3f, -1.0f, 0.0f);

    // Apply thigh rotation
    matrixStack.rotateX(leftThighX);

    // Move to center of thigh
    matrixStack.translate(0.0f, -0.7f, 0.0f);

    // Apply leg customization
    matrixStack.scale(0.4f * leftLegSettings.scaleX, 1.4f * leftLegSettings.scaleY, 0.4f * leftLegSettings.scaleZ);

    // Apply transformations and draw
    glLoadIdentity();
    glTranslatef(0.0f, 0.0f, -8.0f);
    matrixStack.applyToOpenGL();

    drawColoredCube(leftLegSettings.colorR, leftLegSettings.colorG, leftLegSettings.colorB);

    matrixStack.popMatrix();
}

void Application::drawLeftLowerLeg() {
    matrixStack.pushMatrix();

    // Position at bottom left of torso
    matrixStack.translate(-0.3f, -1.0f, 0.0f);

    // Apply thigh rotation
    matrixStack.rotateX(leftThighX);

    // Move to end of thigh (knee position)
    matrixStack.translate(0.0f, -1.4f, 0.0f);

    // Apply lower leg rotation
    matrixStack.rotateX(leftLowerLegX);

    // Move to center of lower leg
    matrixStack.translate(0.0f, -0.6f, 0.0f);

    // Scale to make it look like a lower leg
    matrixStack.scale(0.35f * leftLegSettings.scaleX, 1.2f * leftLegSettings.scaleY, 0.35f * leftLegSettings.scaleZ);

    // Apply transformations and draw
    glLoadIdentity();
    glTranslatef(0.0f, 0.0f, -8.0f);
    matrixStack.applyToOpenGL();

    drawColoredCube(leftLegSettings.colorR, leftLegSettings.colorG, leftLegSettings.colorB);

    matrixStack.popMatrix();
}

void Application::drawRightThigh() {
    matrixStack.pushMatrix();

    // Position at bottom right of torso
    matrixStack.translate(0.3f, -1.0f, 0.0f);

    // Apply thigh rotation
    matrixStack.rotateX(rightThighX);

    // Move to center of thigh
    matrixStack.translate(0.0f, -0.7f, 0.0f);

    // Apply leg customization
    matrixStack.scale(0.4f * rightLegSettings.scaleX, 1.4f * rightLegSettings.scaleY, 0.4f * rightLegSettings.scaleZ);

    // Apply transformations and draw
    glLoadIdentity();
    glTranslatef(0.0f, 0.0f, -8.0f);
    matrixStack.applyToOpenGL();

    drawColoredCube(rightLegSettings.colorR, rightLegSettings.colorG, rightLegSettings.colorB);

    matrixStack.popMatrix();
}

void Application::drawRightLowerLeg() {
    matrixStack.pushMatrix();

    // Position at bottom right of torso
    matrixStack.translate(0.3f, -1.0f, 0.0f);

    // Apply thigh rotation
    matrixStack.rotateX(rightThighX);

    // Move to end of thigh (knee position)
    matrixStack.translate(0.0f, -1.4f, 0.0f);

    // Apply lower leg rotation
    matrixStack.rotateX(rightLowerLegX);

    // Move to center of lower leg
    matrixStack.translate(0.0f, -0.6f, 0.0f);

    // Scale to make it look like a lower leg
    matrixStack.scale(0.35f * rightLegSettings.scaleX, 1.2f * rightLegSettings.scaleY, 0.35f * rightLegSettings.scaleZ);

    // Apply transformations and draw
    glLoadIdentity();
    glTranslatef(0.0f, 0.0f, -8.0f);
    matrixStack.applyToOpenGL();

    drawColoredCube(rightLegSettings.colorR, rightLegSettings.colorG, rightLegSettings.colorB);

    matrixStack.popMatrix();
}
