# Project Name
NAME = humangl

# Compiler and Flags
CCXX = g++
PROCESSORS = -j 4
DEBUG_SYMBOLS = -g3
OPT_LEVEL ?= 0

# Optimization flags based on level
ifeq ($(OPT_LEVEL),0)
	OPT_FLAGS = -O0 $(DEBUG_SYMBOLS)
else ifeq ($(OPT_LEVEL),1)
	OPT_FLAGS = -O1 -flto -s -ffunction-sections -fdata-sections -Wl,--gc-sections
else ifeq ($(OPT_LEVEL),2)
	OPT_FLAGS = -O2 -flto -s -ffunction-sections -fdata-sections -Wl,--gc-sections
else ifeq ($(OPT_LEVEL),3)
	OPT_FLAGS = -O3 -flto -s -ffunction-sections -fdata-sections -Wl,--gc-sections
else
	$(error Unsupported OPT_LEVEL=$(OPT_LEVEL))
endif

# Platform detection
ifeq ($(OS),Windows_NT)
    EXE_EXT := .exe
    MKDIR   = mkdir
    RMDIR   = rmdir /S /Q
    RM      = del /F /Q
else
    EXE_EXT :=
    MKDIR   = mkdir -p
    RMDIR   = rm -rf
    RM      = rm -f
endif

# SDL2 paths (cross-platform)
ifeq ($(OS),Windows_NT)
    SDL2_PREFIX = /usr/local
    INCLUDE_FLAGS = -I$(SDL2_PREFIX)/include
else ifeq ($(shell uname -s),Darwin)
    # macOS (Homebrew)
    SDL2_PREFIX = $(shell brew --prefix sdl2 2>/dev/null || echo /usr/local)
    INCLUDE_FLAGS = -I$(SDL2_PREFIX)/include
else
    # Linux
    INCLUDE_FLAGS = $(shell pkg-config --cflags sdl2 2>/dev/null || echo -I/usr/include/SDL2)
endif

# Compilation flags
FLAGS = -std=c++14 $(OPT_FLAGS) $(EXTRA_FLAGS) -Wall -Wextra -Wmissing-declarations \
		-Wold-style-cast -Wshadow -Wconversion -Wformat=2 -Wundef \
		-Wfloat-equal -Wzero-as-null-pointer-constant $(INCLUDE_FLAGS)

# Directories
OBJECTS = objects
OBJECTS_DEBUG = objects_debug
SOURCES = src
INCLUDES = includes

# Subdirectories for Source and Header files (prepared for future expansion)
CORE_SUBDIRS = Graphics Math Utils
GRAPHICS_SUBDIRS = Rendering Animation Primitives
MATH_SUBDIRS = Matrix Vector Transform
UTILS_SUBDIRS = Input Timer Logger

SUBDIRS = Core $(addprefix Core/,$(CORE_SUBDIRS)) \
		  Graphics $(addprefix Graphics/,$(GRAPHICS_SUBDIRS)) \
		  Math $(addprefix Math/,$(MATH_SUBDIRS)) \
		  Utils $(addprefix Utils/,$(UTILS_SUBDIRS))

# Header Files (organized by category - currently empty, ready for expansion)
# PUT IN ALPHABETICAL ORDER
CORE_HEADERS =
GRAPHICS_HEADERS =
RENDERING_HEADERS =
ANIMATION_HEADERS =
PRIMITIVES_HEADERS =
MATH_HEADERS =
MATRIX_HEADERS =
VECTOR_HEADERS =
TRANSFORM_HEADERS =
UTILS_HEADERS =
INPUT_HEADERS =
TIMER_HEADERS =
LOGGER_HEADERS =

# Combine Header Files
CORE_ALL_HEADERS = $(CORE_HEADERS) $(GRAPHICS_HEADERS) $(RENDERING_HEADERS) $(ANIMATION_HEADERS) $(PRIMITIVES_HEADERS)
MATH_ALL_HEADERS = $(MATH_HEADERS) $(MATRIX_HEADERS) $(VECTOR_HEADERS) $(TRANSFORM_HEADERS)
UTILS_ALL_HEADERS = $(UTILS_HEADERS) $(INPUT_HEADERS) $(TIMER_HEADERS) $(LOGGER_HEADERS)

HEADERS = $(CORE_ALL_HEADERS) $(MATH_ALL_HEADERS) $(UTILS_ALL_HEADERS)

# Generate Source Files by replacing .hpp with .cpp
SUBDIR_SRCS = $(patsubst %.hpp,%.cpp,$(HEADERS))

# Menu system and utility source files
MENU_SRCS = TextRenderer.cpp MenuRenderer.cpp MainMenuRenderer.cpp SettingsMenuRenderer.cpp CreditsMenuRenderer.cpp InstructionsMenuRenderer.cpp BaseMenu.cpp MainMenu.cpp SettingsMenu.cpp CreditsMenu.cpp InstructionsMenu.cpp MouseHandler.cpp MenuInput.cpp MenuSystem.cpp Matrix4.cpp MatrixStack.cpp

# Current source files
MAIN = main.cpp
SRCS = $(SUBDIR_SRCS) $(MAIN) $(MENU_SRCS)

# Use vpath to search for sources and headers in the respective subdirectories
vpath %.cpp $(addprefix $(SOURCES)/,$(SUBDIRS)) $(SOURCES) sources
vpath %.hpp $(addprefix $(INCLUDES)/,$(SUBDIRS)) $(INCLUDES)

# Debug mode configuration
ifeq ($(DEBUG_MODE),1)
    OBJ_DIR = $(OBJECTS_DEBUG)
    TARGET_NAME = $(NAME)_debug$(EXE_EXT)
    EXTRA_FLAGS = -DDEBUG=1
else
    OBJ_DIR = $(OBJECTS)
    TARGET_NAME = $(NAME)$(EXE_EXT)
    EXTRA_FLAGS =
endif

# Generate Object File Paths
OBJ_SUBDIRS = $(patsubst %.cpp,$(OBJ_DIR)/%.o,$(SUBDIR_SRCS))
OBJ_MAIN = $(patsubst %.cpp,$(OBJ_DIR)/%.o,$(MAIN))
OBJ_MENU = $(patsubst %.cpp,$(OBJ_DIR)/%.o,$(MENU_SRCS))
OBJ = $(OBJ_SUBDIRS) $(OBJ_MAIN) $(OBJ_MENU)

# Platform-specific linking flags
ifeq ($(OS),Windows_NT)
    LDFLAGS = -lmingw32 -lSDL2main -lSDL2 -lopengl32 -lglu32
else ifeq ($(shell uname -s),Darwin)
    # macOS
    LDFLAGS = -L$(SDL2_PREFIX)/lib -lSDL2 -framework OpenGL
else
    # Linux
    LDFLAGS = $(shell pkg-config --libs sdl2 2>/dev/null || echo -lSDL2) -lGL -lGLU
endif

# Rules
all: $(TARGET_NAME)

$(TARGET_NAME): $(OBJ) $(HEADERS)
	$(CCXX) $(FLAGS) $(OBJ) -o $(TARGET_NAME) $(LDFLAGS)

$(OBJ_DIR)/%.o: %.cpp
	@$(MKDIR) $(dir $@)
	$(CCXX) $(FLAGS) -c $< -o $@

debug:
	@make all DEBUG_MODE=1 $(PROCESSORS) --no-print-directory 2>/dev/null

clean:
	@test -d $(OBJECTS) && $(RMDIR) $(OBJECTS) || echo "$(OBJECTS) does not exist, skipping."
	@test -d $(OBJECTS_DEBUG) && $(RMDIR) $(OBJECTS_DEBUG) || echo "$(OBJECTS_DEBUG) does not exist, skipping."

fclean: clean
	@test -f $(NAME)$(EXE_EXT) && $(RM) $(NAME)$(EXE_EXT) || echo "$(NAME)$(EXE_EXT) does not exist, skipping."
	@test -f $(NAME)_debug$(EXE_EXT) && $(RM) $(NAME)_debug$(EXE_EXT) || echo "$(NAME)_debug$(EXE_EXT) does not exist, skipping."

re: fclean
	@make all $(PROCESSORS) --no-print-directory 2>/dev/null

.PHONY: all clean fclean re debug
