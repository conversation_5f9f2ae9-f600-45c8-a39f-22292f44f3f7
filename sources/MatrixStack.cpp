// Use the corresponding header file which includes all necessary CStyle dependencies
#include "../includes/MatrixStack.hpp"

MatrixStack::MatrixStack() {
    current.loadIdentity();
}

void MatrixStack::loadIdentity() {
    current.loadIdentity();
}

void MatrixStack::pushMatrix() {
    stack.push_back(current);
}

void MatrixStack::popMatrix() {
    if (!stack.empty()) {
        current = stack.back();
        stack.pop_back();
    }
}

void MatrixStack::translate(float x, float y, float z) {
    current.translate(x, y, z);
}

void MatrixStack::rotateX(float angle) {
    current.rotateX(angle);
}

void MatrixStack::rotateY(float angle) {
    current.rotateY(angle);
}

void MatrixStack::rotateZ(float angle) {
    current.rotateZ(angle);
}

void MatrixStack::scale(float x, float y, float z) {
    current.scale(x, y, z);
}

void MatrixStack::applyToOpenGL() {
    glLoadIdentity();
    current.applyToOpenGL();
}
